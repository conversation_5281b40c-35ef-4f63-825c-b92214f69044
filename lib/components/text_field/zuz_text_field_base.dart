import 'package:flutter/material.dart';
import '../../theme/theme.dart';

/// Componente base para campos de texto do aplicativo seguindo o Design System
class ZuzTextFieldBase extends StatelessWidget {
  /// Label do campo
  final String? label;
  
  /// Se o campo é opcional (mostra texto "Opcional")
  final bool isOptional;
  
  /// Widget filho que será o input/dropdown/multiline específico
  final Widget child;
  
  /// Texto de ajuda (helper text)
  final String? helperText;
  
  /// Contador de caracteres (formato "0/100")
  final String? characterCounter;
  
  /// Mensagem de erro personalizada
  final String? errorText;

  /// Texto para "Opcional" (fornecido pelos componentes que usam este base)
  final String optionalText;

  const ZuzTextFieldBase({
    super.key,
    this.label,
    required this.child,
    required this.optionalText,
    this.isOptional = false,
    this.helperText,
    this.characterCounter,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Row com label e indicação de opcional (se houver label)
        if (label != null && label!.isNotEmpty) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label principal
              Text(
                label!,
                style: const TextStyle(
                  fontSize: 14,
                  height: 21 / 14, // lineHeight = 21.sp
                  fontWeight: FontWeight.w700, // FontWeight(700)
                  color: AppColors.black,
                ),
              ),
              
              // Texto "Opcional" se necessário
              if (isOptional)
                Text(
                  optionalText,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 21 / 14, // lineHeight = 21.sp
                    fontWeight: FontWeight.w400, // FontWeight(400)
                    color: AppColors.gray5,
                  ),
                  textAlign: TextAlign.right,
                ),
            ],
          ),

          const SizedBox(height: 4), // spacedBy(4.dp)
        ],
        
        // Componente filho (input/dropdown/multiline específico)
        child,
        
        // Helper text (se existir)
        if (helperText != null) ...[
          const SizedBox(height: 4),
          Text(
            helperText!,
            style: const TextStyle(
              fontSize: 12,
              height: 21 / 12, // lineHeight = 21.sp
              fontWeight: FontWeight.w400,
              color: AppColors.gray5,
            ),
          ),
        ],
        
        // Contador de caracteres (se existir)
        if (characterCounter != null) ...[
          const SizedBox(height: 4),
          Align(
            alignment: Alignment.centerRight,
            child: Text(
              characterCounter!,
              style: const TextStyle(
                fontSize: 14,
                height: 21 / 14, // lineHeight = 21.sp
                fontWeight: FontWeight.w400,
                color: AppColors.gray5,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
        
        // Mensagem de erro (se existir)
        if (errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            errorText!,
            style: const TextStyle(
              fontSize: 14,
              height: 21 / 14, // lineHeight = 21.sp
              fontWeight: FontWeight.w400,
              color: AppColors.error,
            ),
          ),
        ],
      ],
    );
  }
}
