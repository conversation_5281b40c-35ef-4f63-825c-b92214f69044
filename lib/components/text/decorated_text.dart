import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../../theme/colors.dart';

class DecoratedText extends StatelessWidget {
  /// Decora um trecho de texto. A parte decorada precisa estar envolta com chaves ('{' e '}').
  /// Também é possível definir uma ação ao interagir com cada trecho decorado com a propriedade `actions`.
  const DecoratedText(
    this.text, {
    super.key,
    this.actions,
    this.decoratedTextStyle,
    this.maxLines,
    this.textAlign = TextAlign.start,
    this.textStyle,
  });

  /// A parte decorada precisa estar envolta com chaves ('{' e '}').
  /// Exemplo de valor:
  ///
  /// "Concordo com os {termos de custódia da corretora}"
  final String text;

  /// Para cada trecho decorado no texto, define o que fazer quando um clique é recebido nesse trecho.
  /// Quando actions for definido, deve ter no mínimo o mesmo nº de ações em relação ao nº de trechos decorados.
  /// Exemplo de valor:
  ///
  /// ```dart
  /// [new TapGestureRecognizer()..onTap = () => Get.toNamed(TermsPage.routeName)]
  /// ```
  final List<GestureRecognizer>? actions;

  /// O estilo do texto decorado sempre é combinado com o estilo do texto comum.
  ///
  /// Por padrão, é [textStyle] com `fontWeight: FontWeight.bold` e `color: AppColors.primary`.
  final TextStyle? decoratedTextStyle;

  final int? maxLines;

  final TextAlign textAlign;

  /// O estilo do texto como um todo, com exceção dos trechos decorados.
  ///
  /// Por padrão, é `bodyMedium`.
  final TextStyle? textStyle;

  TextStyle? _defaultTextStyle(BuildContext context) =>
      TextTheme.of(context).bodyMedium;

  List<TextSpan> _splitText(BuildContext context) {
    List<TextSpan> content = [];
    int action = 0;

    text.splitMapJoin(
      RegExp(r'\{(.*?)\}'),
      onMatch: (m) {
        content.add(
          TextSpan(
            text: m.group(1),
            style:
                decoratedTextStyle ??
                _defaultTextStyle(context)?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
            recognizer: actions != null ? actions![action] : null,
          ),
        );
        action++;
        return '';
      },
      onNonMatch: (nm) {
        content.add(TextSpan(text: nm));
        return '';
      },
    );

    return content;
  }

  @override
  Widget build(BuildContext context) {
    List<TextSpan> content = _splitText(context);

    return RichText(
      maxLines: maxLines,
      textAlign: textAlign,
      text: TextSpan(
        text: content.first.text,
        style: textStyle ?? _defaultTextStyle(context),
        children: <TextSpan>[...content.getRange(1, content.length)],
      ),
    );
  }
}
