import 'package:flutter/material.dart';

import '../../theme/colors.dart';

class BackgroundPatternDecoration extends BoxDecoration {
  BackgroundPatternDecoration({bool isUpsideDown = false})
    : super(
        image: DecorationImage(
          alignment: isUpsideDown ? Alignment.bottomCenter : Alignment.topCenter,
          image: AssetImage('assets/images/background_pattern.png'),
          fit: BoxFit.fitWidth,
        ),
        color: AppColors.gray2,
      );
}
