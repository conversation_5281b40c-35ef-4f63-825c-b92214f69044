# Cards Components

Este diretório contém os componentes de cards da aplicação.

## ZuzChip

### Descrição
Componente `ZuzChip` — chip reutilizável baseado no `Chip` do Material.

### Uso
```dart
ZuzChip(
  value: AppLocalizations.of(context)!.someLabel,
  type: ChipType.success,
  onDeleted: () { /* ação */ },
)
```

## ZuzCard

### Descrição
Widget de card customizado para exibir informações estruturadas com header personalizado, lista de dados e opcionalmente mini cards organizados em grid.

### Funcionalidades
- **Header**: Background customizado usando `assets/patterns/card_header.png`, título e botão de opções opcional
- **Seção de Dados**: Lista de informações no formato chave-valor
- **Mini Cards**: Grid de 2 colunas com mini cards (opcional)

### Parâmetros Obrigatórios
- `title` (String): Título exibido no header
- `data` (List<Map<String, String>>): Lista de dados principais, cada mapa deve conter 'label' e 'value'

### Parâmetros Opcionais
- `onOptionsPressed`: Callback para o botão de opções no header
- `miniCards`: Define se exibe os mini cards (padrão: false)
- `miniCardsTitle`: Título da seção de mini cards
- `miniCardsData`: Dados para os mini cards
- `onMiniCardsTitlePressed`: Callback para o título da seção mini cards

### Exemplo de Uso
```dart
ZuzCard(
  title: 'Lote 02',
  data: [
    {'label': 'Animais (un)', 'value': '300'},
    {'label': 'Propriedade', 'value': 'Boas Novas'},
  ],
  miniCards: true,
  miniCardsTitle: 'Resumo abate lote',
  miniCardsData: [
    {'label': 'Ciclo abate', 'value': '23 m'},
    {'label': 'Peso abate', 'value': '555 kg'},
  ],
)
```

## Regras Gerais

- Não adicionar textos fixos no widget — strings devem vir do sistema de localização.
- Todas as cores devem vir de `AppColors` (arquivo `lib/theme/colors.dart`).
- Imports devem estar em ordem alfabética.
- Ao editar qualquer widget, atualize este README com mudanças importantes.
