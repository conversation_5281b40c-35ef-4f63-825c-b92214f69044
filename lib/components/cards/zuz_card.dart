import 'package:flutter/material.dart';

import '../../localization/repositories/locale_repository.dart';
import '../../locator/locator.dart';
import '../../theme/colors.dart';

/// Widget de card customizado para exibir informações estruturadas
///
/// Este widget exibe um card com header personalizado, lista de informações
/// e opcionalmente mini cards organizados em grid
class ZuzCard extends StatefulWidget {
  /// Título exibido no header do card
  final String title;
  
  /// Lista de dados do card, onde cada mapa contém 'label' e 'value'
  final List<Map<String, String>> data;

  /// Widget opcional de status que será renderizado como o primeiro item em data
  final Widget? statusWidget;
  
  /// Callback executado quando o botão de opções é pressionado
  final VoidCallback? onOptionsPressed;
  
  /// Define se deve exibir os mini cards
  final bool miniCards;
  
  /// Título da seção de mini cards
  final String? miniCardsTitle;
  
  /// Lista de dados para os mini cards, onde cada mapa contém 'label' e 'value'
  final List<Map<String, String>>? miniCardsData;
  
  /// Callback executado quando o título da seção mini cards é pressionado
  final VoidCallback? onMiniCardsTitlePressed;

  /// Botão de ação exibido na parte inferior do card (opcional)
  final Widget? actionButton;

  const ZuzCard({
    super.key,
    required this.title,
    required this.data,
    this.onOptionsPressed,
    this.miniCards = false,
    this.miniCardsTitle,
    this.miniCardsData,
    this.onMiniCardsTitlePressed,
  this.statusWidget,
  this.actionButton,
  });

  @override
  State<ZuzCard> createState() => _ZuzCardState();
}

class _ZuzCardState extends State<ZuzCard> {
  bool _miniCardsExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.gray1,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildHeader(),
          _buildDataSection(),
          if (widget.miniCards) ...[
            _buildMiniCardsTitle(),
            AnimatedCrossFade(
              firstChild: const SizedBox.shrink(),
              secondChild: _buildMiniCardsGrid(),
              crossFadeState: _miniCardsExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
              duration: const Duration(milliseconds: 250),
              firstCurve: Curves.easeOut,
              secondCurve: Curves.easeIn,
              sizeCurve: Curves.easeInOut,
            ),
          ],
          if (widget.actionButton != null)
            Padding(
              padding: const EdgeInsets.all(16),
              child: widget.actionButton,
            ),
        ],
      ),
    );
  }

  /// Constrói o header do card com background personalizado
  Widget _buildHeader() {
    return Container(
      height: 64,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/patterns/card_header.png'),
          fit: BoxFit.cover,
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(4),
          topRight: Radius.circular(4),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            Expanded(
        child: Text(
          widget.title,
                style: const TextStyle(
                  fontFamily: 'Century Gothic',
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.black,
                  height: 1.4,
                ),
              ),
            ),
            if (widget.onOptionsPressed != null)
              GestureDetector(
                onTap: widget.onOptionsPressed,
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Icons.more_vert,
                    color: AppColors.black,
                    size: 20,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Constrói a seção de dados principais do card
  Widget _buildDataSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: _buildDataWithStatus(),
      ),
    );
  }

  List<Widget> _buildDataWithStatus() {
    final widgets = <Widget>[];

    // se houver statusWidget, adiciona como primeiro item com label de localization
    if (widget.statusWidget != null) {
      final statusLabel = Locator.instance.get<LocaleRepository>().strings.componentsStrings.status;
      widgets.add(_buildStatusRow(statusLabel, widget.statusWidget!));
    }

    widgets.addAll(widget.data.map((item) => _buildDataRow(item)).toList());
    return widgets;
  }

  /// Constrói uma linha de dados
  Widget _buildDataRow(Map<String, String> item) {
  final label = item['label'] ?? '';
  final value = item['value'] ?? '';
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 128,
            child: Text(
              label,
              style: const TextStyle(
                fontFamily: 'Century Gothic',
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: AppColors.gray5,
                height: 1.5,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontFamily: 'Century Gothic',
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: AppColors.black,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, Widget status) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 128,
            child: Text(
              label,
              style: const TextStyle(
                fontFamily: 'Century Gothic',
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: AppColors.gray5,
                height: 1.5,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Align(
              alignment: Alignment.centerLeft,
              child: status,
            ),
          ),
        ],
      ),
    );
  }

  /// Constrói o título da seção de mini cards
  Widget _buildMiniCardsTitle() {
  if (widget.miniCardsTitle == null) return const SizedBox.shrink();
    // título sempre clicável para expandir/colapsar; ícone sempre visível
    return GestureDetector(
      onTap: () {
        widget.onMiniCardsTitlePressed?.call();
        setState(() {
          _miniCardsExpanded = !_miniCardsExpanded;
        });
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            Expanded(
              child: Text(
                widget.miniCardsTitle!,
                style: const TextStyle(
                  fontFamily: 'Century Gothic',
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppColors.black,
                  height: 1.5,
                ),
              ),
            ),
            Container(
              width: 20,
              height: 20,
              alignment: Alignment.center,
              child: AnimatedRotation(
                turns: _miniCardsExpanded ? 0.5 : 0.0,
                duration: const Duration(milliseconds: 200),
                child: const Icon(
                  Icons.keyboard_arrow_down,
                  color: AppColors.black,
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Constrói o grid de mini cards
  Widget _buildMiniCardsGrid() {
  if (widget.miniCardsData == null || widget.miniCardsData!.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 2.4,
        ),
        itemCount: widget.miniCardsData!.length,
        itemBuilder: (context, index) {
          return _buildMiniCard(widget.miniCardsData![index]);
        },
      ),
    );
  }

  /// Constrói um mini card individual
  Widget _buildMiniCard(Map<String, String> item) {
    final label = item['label'] ?? '';
    final value = item['value'] ?? '';
    
    return Container(
      decoration: BoxDecoration(
        color: AppColors.primaryLight,
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontFamily: 'Century Gothic',
              fontSize: 12,
              fontWeight: FontWeight.normal,
              color: AppColors.black,
              height: 1.75,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Century Gothic',
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.black,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
