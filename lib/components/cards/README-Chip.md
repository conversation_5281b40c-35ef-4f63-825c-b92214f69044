ZuzChip
=======

Descrição
---------

Componente `ZuzChip` — chip reutilizável baseado no `Chip` do Material.

Regras
------

- Não adicionar textos fixos no widget — strings devem vir do sistema de localização.
- <PERSON>das as cores devem vir de `AppColors` (arquivo `lib/theme/colors.dart`).
- Imports devem estar em ordem alfabética.
- Ao editar esse widget, atualize este README com mudanças importantes.

Uso
---

Exemplo:

```dart
ZuzChip(
  value: AppLocalizations.of(context)!.someLabel,
  type: ChipType.success,
  onDeleted: () { /* ação */ },
)
```
