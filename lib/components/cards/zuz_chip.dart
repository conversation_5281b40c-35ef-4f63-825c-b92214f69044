import 'package:flutter/material.dart';

import '../../theme/colors.dart';

/// Chip customizado do design system.
///
/// Entradas:
/// - value: texto a ser exibido
/// - type: tipo do chip (neutral, success, warning, error, icon)
/// - onDeleted: callback chamado quando o usuário clica no X
class ZuzChip extends StatelessWidget {
  final String? value;
  final Widget? icon;
  final ChipType type;
  final VoidCallback? onDeleted;
  final bool showBullet;
  final bool showClose;

  const ZuzChip({
    super.key,
    this.value,
    this.icon,
    this.type = ChipType.neutral,
    this.onDeleted,
  this.showBullet = true,
  this.showClose = true,
  })  : assert(type == ChipType.icon ? icon != null : value != null && value != ''),
        assert(type == ChipType.icon ? value == null : icon == null);

  Color get _backgroundColor {
    switch (type) {
      case ChipType.success:
        return AppColors.successBoxes;
      case ChipType.warning:
        return AppColors.warningBoxes;
      case ChipType.error:
        return AppColors.errorBoxes;
      case ChipType.icon:
        return AppColors.gray0;
      case ChipType.neutral:
        return AppColors.gray0;
    }
  }

  Color get _textColor {
    switch (type) {
      case ChipType.success:
        return AppColors.successTexts;
      case ChipType.warning:
        return AppColors.warningTexts;
      case ChipType.error:
        return AppColors.errorTexts;
      case ChipType.icon:
        return AppColors.systemInfoTexts;
      case ChipType.neutral:
        return AppColors.black;
    }
  }

  Color get _dotColor {
    switch (type) {
      case ChipType.success:
        return AppColors.success;
      case ChipType.warning:
        return AppColors.warning;
      case ChipType.error:
        return AppColors.error;
      case ChipType.icon:
        return AppColors.systemInfo;
      case ChipType.neutral:
        return AppColors.gray6;
    }
  }



  @override
  Widget build(BuildContext context) {
    return Chip(
      backgroundColor: _backgroundColor,
  // avatar removed: icon is placed inside the label when type == ChipType.icon
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: const BorderSide(color: Colors.transparent, width: 0),
      ),
      padding: EdgeInsets.zero,
      labelPadding: type == ChipType.icon && !showClose  ? EdgeInsets.zero : const EdgeInsets.only(left: 8),
      label: type == ChipType.icon
          ? icon!
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (showBullet) ...[
                  Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _dotColor,
                    ),
                  ),
                  const SizedBox(width: 6),
                ],
                Text(
                  value!,
                  style: TextStyle(
                    color: _textColor,
                    fontSize: 14,
                  ),
                ),
                if(!showClose)
                  const SizedBox(width: 10),
              ],
            ),
      onDeleted: showClose ? onDeleted : null,
      deleteIcon: showClose
          ? Icon(
              Icons.close,
              size: 18,
              color: AppColors.gray6,
            )
          : null,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
    );
  }
}

enum ChipType { neutral, success, warning, error, icon }
