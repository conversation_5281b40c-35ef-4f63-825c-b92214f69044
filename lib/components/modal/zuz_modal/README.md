ZuzModal
=======

Componente `ZuzModal` — componente para exibir diálogos estilizados seguindo o design system.

Uso rápido
---------

ZuzModal.show(
  context,
  icon: Icon(Icons.check_circle, color: AppColors.success, size: 40),
  title: 'Título do <PERSON>dal',
  description: 'Uma breve descrição do conteúdo do modal.',
  actionButtons: [
    // Primary primeiro
    ZuzButton(text: 'Confirmar', onPressed: () => Navigator.of(context).pop()),
    // Secondary depois
    ZuzButton(text: 'Cancelar', variant: ZuzButtonVariant.secondary, onPressed: () => Navigator.of(context).pop()),
  ],
);

Notas
-----
- O `ZuzModal` respeita o tema global (font-family) do app. Evite redefinir a família de fontes localmente.
- Para ícones e ações, prefira usar `AppColors` e `ZuzButton` respectivamente para manter consistência.
# ZuzModal

Componente de modal do design system ZUZ, para uso com `showDialog` do Flutter.

- Raio: 4px
- Fundo: AppColors.white
- Sombra: 0 20px 25px -5px rgba(0, 0, 0, 0.10), 0 10px 10px -5px rgba(0, 0, 0, 0.04)
- Não utilizar cores hardcoded fora do AppColors.
- Sem textos fixos: títulos/descrições vêm de strings (localização do app).

## API

ZuzModal(
  icon: Widget?,
  title: String?,
  description: String?,
  child: Widget?,
  showCloseButton: bool = true,
  actionButtons: List<Widget>?,
  onClose: VoidCallback?,
  padding: EdgeInsetsGeometry = EdgeInsets.fromLTRB(24, 24, 24, 16),
  actionsSpacing: double = 8.0,
  maxWidth: double = 560,
)

### Abrindo com helper

ZuzModal.show(
  context,
  title: strings.componentsStrings.modalTitle,
  description: strings.componentsStrings.modalDescription,
  actionButtons: [
    // seus botões
  ],
);

## Boas práticas
- Imports em ordem alfabética.
- Atualize este README ao evoluir o componente.
