import 'package:flutter/material.dart';
import 'package:zuz_app/theme/colors.dart';

/// ZuzModal
///
/// Um conteúdo personalizável para ser usado dentro do `showDialog` padrão
/// do Flutter. Respeita as diretrizes do design system (raio 4, sombra e
/// fundo branco) e permite compor ícone, título, descrição, conteúdo e ações.
class ZuzModal extends StatelessWidget {
  const ZuzModal({
    super.key,
    this.icon,
    this.title,
    this.description,
    this.child,
    this.showCloseButton = true,
    this.actionButtons,
    this.onClose,
    this.padding = const EdgeInsets.fromLTRB(24, 24, 24, 16),
    this.actionsSpacing = 8.0,
    this.maxWidth = 560,
  });

  /// Ícone opcional exibido no topo do modal.
  final Widget? icon;

  /// Título opcional.
  final String? title;

  /// Descrição opcional.
  final String? description;

  /// Conteúdo principal opcional.
  final Widget? child;

  /// Exibe o botão de fechar no canto superior direito. Padrão: true.
  final bool showCloseButton;

  /// Lista de botões de ação (ex.: primário e secundário).
  final List<Widget>? actionButtons;

  /// Callback disparado quando o botão de fechar é pressionado.
  final VoidCallback? onClose;

  /// Espaçamento interno do conteúdo.
  final EdgeInsetsGeometry padding;

  /// Espaçamento horizontal entre botões de ação.
  final double actionsSpacing;

  /// Largura máxima do conteúdo do modal.
  final double maxWidth;

  /// Helper para abrir o modal usando o `showDialog` padrão.
  static Future<T?> show<T>(
    BuildContext context, {
    Widget? icon,
    String? title,
    String? description,
    Widget? child,
    bool showCloseButton = true,
    List<Widget>? actionButtons,
    VoidCallback? onClose,
    double actionsSpacing = 8.0,
    double maxWidth = 560,
    bool barrierDismissible = true,
    RouteSettings? routeSettings,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      useSafeArea: true,
      routeSettings: routeSettings,
      builder: (ctx) => ZuzModal(
        icon: icon,
        title: title,
        description: description,
        child: child,
        showCloseButton: showCloseButton,
        actionButtons: actionButtons,
        onClose: onClose,
        actionsSpacing: actionsSpacing,
        maxWidth: maxWidth,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      insetPadding: const EdgeInsets.all(16),
      child: Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(minWidth: 280, maxWidth: maxWidth),
          child: Stack(
            children: [
              // Card base com radius 4, fundo branco e sombra personalizada
              DecoratedBox(
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(4),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.black.withOpacity(0.10),
                      offset: const Offset(0, 20),
                      blurRadius: 25,
                      spreadRadius: -5,
                    ),
                    BoxShadow(
                      color: AppColors.black.withOpacity(0.04),
                      offset: const Offset(0, 10),
                      blurRadius: 10,
                      spreadRadius: -5,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 20, 16, 16),
                      child: Column( // Largura total e alinhamento à esquerda
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        spacing: 12,
                        children: [
                          if (icon != null)
                            Align(
                              alignment: Alignment.centerLeft,
                              child: icon!,
                            ),
                          if (title != null || description != null)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              spacing: 8,
                              children: [
                                if (title != null)
                                  Text(
                                    title!,
                                    style: const TextStyle(
                                      color: AppColors.black, // neutral-black-text
                                      fontSize: 18,
                                      fontStyle: FontStyle.normal,
                                      fontWeight: FontWeight.w600,
                                      height: 27 / 18, // 150%
                                    ),
                                  ), // Formatação de título
                                if (description != null)
                                  Text(
                                    description!,
                                    style: const TextStyle(
                                      color: AppColors.gray5, // Neutral Gray 5
                                      fontSize: 12,
                                      fontStyle: FontStyle.normal,
                                      fontWeight: FontWeight.w400,
                                      height: 21 / 12, // 175%
                                    ),
                                  ), // Formatação de descrição
                              ],
                            ),
                          if (child != null) child!,
                        ],
                      ),
                    ),
                      if (actionButtons != null && actionButtons!.isNotEmpty)
                        Container(
                          decoration: const BoxDecoration(
                            color: AppColors.gray1,
                            borderRadius: BorderRadius.only(
                              bottomLeft: Radius.circular(4),
                              bottomRight: Radius.circular(4),
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            spacing: 16,
                            children: actionButtons!,
                          ),
                        ),
                  ],
                ),
              ),

              if (showCloseButton)
                Positioned(
                  top: 4,
                  right: 4,
                  child: IconButton(
                    visualDensity: VisualDensity.compact,
                    splashRadius: 18,
                    onPressed: () {
                      onClose?.call();
                      Navigator.of(context).maybePop();
                    },
                    icon: const Icon(Icons.close, size: 20),
                    color: AppColors.gray5,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
