# ZuzDatePicker

Componentes para seleção de datas no aplicativo Zuz.

## Componentes

### ZuzDatePicker
Modal de calendário para seleção de datas (única ou intervalo).

### ZuzDatePickerField
Campo de texto que abre o ZuzDatePicker ao ser clicado, mantendo a aparência consistente com outros campos do formulário.

## ZuzDatePicker

Widget de calendário customizado para seleção de datas.

### Características

- **Seleção única ou intervalo**: Configurável via `ZuzDatePickerType`
- **Interface intuitiva**: Calendário visual único com navegação por meses
- **Suporte a pré-seleção**: Permite inicializar com datas já selecionadas
- **Ações claras**: Botões para aplicar filtro, fechar e limpar seleção
- **Responsivo**: Adapta-se bem a diferentes tamanhos de tela
- **Calendário único**: Tanto para seleção única quanto para intervalos, utiliza apenas um calendário

### Uso

```dart
// Modal direto
final result = await showZuzDatePicker(
  context: context,
  type: ZuzDatePickerType.single,
  initialDate: DateTime.now(),
);

// Widget customizado
ZuzDatePicker(
  type: ZuzDatePickerType.range,
  initialStartDate: startDate,
  initialEndDate: endDate,
  onApply: (result) => print('Selecionado: $result'),
  onClose: () => Navigator.pop(context),
)
```

## ZuzDatePickerField

Campo de texto que combina a aparência do ZuzTextField com a funcionalidade do ZuzDatePicker.

### Características

- **Aparência consistente**: Segue o mesmo design system dos outros campos
- **Placeholder inteligente**: Mostra formato esperado quando vazio
- **Formatação automática**: Formata datas automaticamente para exibição
- **Ícone visual**: Ícone de calendário para indicar funcionalidade
- **Estados visuais**: Suporte a foco, erro, desabilitado
- **Acessibilidade**: Mantém compatibilidade com foco e navegação por teclado

### Uso

```dart
// Data única
ZuzDatePickerField(
  label: 'Data de nascimento',
  type: ZuzDatePickerType.single,
  placeholder: 'DD/MM/AAAA',
  onChanged: (result) => print('Data: ${result.selectedDate}'),
)

// Período (como no Figma)
ZuzDatePickerField(
  label: 'Período de avaliação',
  type: ZuzDatePickerType.range,
  placeholder: 'MM/AAAA - MM/AAAA',
  onChanged: (result) => print('Período: ${result.startDate} - ${result.endDate}'),
)
```

### Parâmetros

#### Obrigatórios
- `label`: Label do campo

#### Opcionais
- `type`: Tipo de seleção (padrão: single)
- `placeholder`: Texto quando vazio
- `helperText`: Texto de ajuda
- `errorText`: Mensagem de erro
- `enabled`: Se o campo está habilitado (padrão: true)
- `isOptional`: Se mostra texto "Opcional" (padrão: false)
- `initialDate`: Data inicial para tipo single
- `initialStartDate/initialEndDate`: Datas iniciais para tipo range
- `firstDate/lastDate`: Limites de data selecionável
- `onChanged`: Callback quando seleção muda

## ZuzDatePickerResult

Objeto que representa o resultado da seleção.

```dart
class ZuzDatePickerResult {
  final DateTime? selectedDate;    // Para tipo single
  final DateTime? startDate;       // Para tipo range
  final DateTime? endDate;         // Para tipo range
  
  bool get hasSelection;           // Se há alguma seleção
  ZuzDatePickerResult clear();     // Limpa seleções
}
```

## Tipos de Seleção

```dart
enum ZuzDatePickerType {
  single,      // Uma única data
  range,       // Intervalo de datas
}
```

## Formatação

- **Data única**: DD/MM/AAAA (ex: 15/08/2023)
- **Período**: MM/AAAA - MM/AAAA (ex: 08/2023 - 12/2023)
- **Placeholder**: Segue o formato esperado

## Integração com Forms

O `ZuzDatePickerField` pode ser facilmente integrado com formulários:

```dart
class MyForm extends StatefulWidget {
  @override
  _MyFormState createState() => _MyFormState();
}

class _MyFormState extends State<MyForm> {
  ZuzDatePickerResult? _dateResult;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ZuzDatePickerField(
          label: 'Data',
          onChanged: (result) => setState(() => _dateResult = result),
        ),
        // Outros campos...
      ],
    );
  }
}
```

## Observações

- Use `ZuzDatePicker` quando precisar de controle total sobre o modal
- Use `ZuzDatePickerField` quando quiser integração simples com formulários
- O `ZuzDatePickerField` mantém foco e navegação por teclado consistentes
- Ambos seguem o Design System Zuz com cores e tipografia padronizadas
