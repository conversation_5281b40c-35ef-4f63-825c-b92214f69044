import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../locator/locator.dart';
import '../../localization/repositories/locale_repository.dart';
import '../../theme/theme.dart';

/// Callback para mudanças na data de nascimento
typedef BirthDateChangedCallback = void Function(int? day, int? month, int? year);

/// Widget para entrada de data de nascimento com campos separados
/// 
/// Permite entrada separada de dia (opcional), mês e ano.
/// Calcula e exibe a idade automaticamente quando a data for válida.
class ZuzBirthDateField extends StatefulWidget {
  /// Label do campo
  final String label;
  
  /// Se o dia é opcional
  final bool isDayOptional;
  
  /// Valor inicial do dia
  final int? initialDay;
  
  /// Valor inicial do mês
  final int? initialMonth;
  
  /// Valor inicial do ano
  final int? initialYear;
  
  /// Callback chamado quando a data muda
  final BirthDateChangedCallback? onChanged;
  
  /// Se o campo está habilitado
  final bool enabled;

  const ZuzBirthDateField({
    super.key,
    required this.label,
    this.isDayOptional = true,
    this.initialDay,
    this.initialMonth,
    this.initialYear,
    this.onChanged,
    this.enabled = true,
  });

  @override
  State<ZuzBirthDateField> createState() => _ZuzBirthDateFieldState();
}

class _ZuzBirthDateFieldState extends State<ZuzBirthDateField> {
  late TextEditingController _dayController;
  late TextEditingController _monthController;
  late TextEditingController _yearController;
  
  late FocusNode _dayFocusNode;
  late FocusNode _monthFocusNode;
  late FocusNode _yearFocusNode;
  
  int? _day;
  int? _month;
  int? _year;

  @override
  void initState() {
    super.initState();
    
    _day = widget.initialDay;
    _month = widget.initialMonth;
    _year = widget.initialYear;
    
    _dayController = TextEditingController(
      text: _day?.toString().padLeft(2, '0') ?? '',
    );
    _monthController = TextEditingController(
      text: _month?.toString().padLeft(2, '0') ?? '',
    );
    _yearController = TextEditingController(
      text: _year?.toString() ?? '',
    );
    
    _dayFocusNode = FocusNode();
    _monthFocusNode = FocusNode();
    _yearFocusNode = FocusNode();
    
    _dayController.addListener(_onDayChanged);
    _monthController.addListener(_onMonthChanged);
    _yearController.addListener(_onYearChanged);
  }

  @override
  void dispose() {
    _dayController.dispose();
    _monthController.dispose();
    _yearController.dispose();
    _dayFocusNode.dispose();
    _monthFocusNode.dispose();
    _yearFocusNode.dispose();
    super.dispose();
  }

  void _onDayChanged() {
    final value = _dayController.text;
    if (value.isEmpty) {
      _day = null;
    } else {
      final parsed = int.tryParse(value);
      if (parsed != null && parsed >= 1 && parsed <= 31) {
        _day = parsed;
        if (value.length == 2) {
          _monthFocusNode.requestFocus();
        }
      } else {
        _day = null;
      }
    }
    _notifyChange();
  }

  void _onMonthChanged() {
    final value = _monthController.text;
    if (value.isEmpty) {
      _month = null;
    } else {
      final parsed = int.tryParse(value);
      if (parsed != null && parsed >= 1 && parsed <= 12) {
        _month = parsed;
        if (value.length == 2) {
          _yearFocusNode.requestFocus();
        }
      } else {
        _month = null;
      }
    }
    _notifyChange();
  }

  void _onYearChanged() {
    final value = _yearController.text;
    if (value.isEmpty) {
      _year = null;
    } else {
      final parsed = int.tryParse(value);
      if (parsed != null && parsed >= 1900 && parsed <= DateTime.now().year) {
        _year = parsed;
      } else {
        _year = null;
      }
    }
    _notifyChange();
  }

  void _notifyChange() {
  // Atualiza a interface localmente e notifica o callback externo
  if (mounted) setState(() {});
  widget.onChanged?.call(_day, _month, _year);
  }

  /// Calcula a idade baseada na data de nascimento
  int? _calculateAge() {
    // Se dia não é opcional e não foi preenchido, não calcula
    if (!widget.isDayOptional && _day == null) return null;
    
    if (_month == null || _year == null) return null;
    
    final now = DateTime.now();
    final birthDate = DateTime(
      _year!,
      _month!,
      _day ?? 1, // Se dia é opcional ou não preenchido, usa 1
    );
    
    if (birthDate.isAfter(now)) return null;
    
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    
    return age >= 0 ? age : null;
  }

  @override
  Widget build(BuildContext context) {
    final age = _calculateAge();
    final strings = Locator.instance.get<LocaleRepository>().strings;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label e indicação de dia opcional
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label principal
            Expanded(
              child: Text(
                widget.label,
                style: const TextStyle(
                  fontSize: 14,
                  height: 21 / 14, // lineHeight = 21px
                  fontWeight: FontWeight.w700, // Bold
                  color: AppColors.black, // #111827
                ),
              ),
            ),
            
            // Texto "Dia opcional" se necessário
            if (widget.isDayOptional)
              Text(
                strings.componentsStrings.dayOptional,
                style: const TextStyle(
                  fontSize: 14,
                  height: 21 / 14, // lineHeight = 21px
                  fontWeight: FontWeight.w400, // Regular
                  color: AppColors.gray5, // #6B7280
                ),
                textAlign: TextAlign.right,
              ),
          ],
        ),
        
        const SizedBox(height: 4), // gap-1 = 4px
        
        // Campos de entrada
        Row(
          children: [
            // Campo do dia (sempre visível)
            Expanded(
              child: _buildTextField(
                controller: _dayController,
                focusNode: _dayFocusNode,
                hintText: 'DD',
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(2),
                  _DayInputFormatter(),
                ],
              ),
            ),
            
            const SizedBox(width: 8), // gap-2 = 8px
            
            // Campo do mês
            Expanded(
              child: _buildTextField(
                controller: _monthController,
                focusNode: _monthFocusNode,
                hintText: 'MM',
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(2),
                  _MonthInputFormatter(),
                ],
              ),
            ),
            
            const SizedBox(width: 8), // gap-2 = 8px
            
            // Campo do ano
            Expanded(
              child: _buildTextField(
                controller: _yearController,
                focusNode: _yearFocusNode,
                hintText: 'AAAA',
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(4),
                  _YearInputFormatter(),
                ],
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8), // gap-2 = 8px
        
        // Exibição da idade (sempre visível)
        Row(
          children: [
            // Ícone do bolo (18x18px)
            Container(
              width: 18,
              height: 18,
              alignment: Alignment.center,
              child: const Icon(
                Icons.cake_outlined,
                size: 14, // centralizado dentro do container 18x18
                color: AppColors.primary, // cor Brand/Primary do Figma
              ),
            ),
            
            const SizedBox(width: 4), // gap-1 = 4px
            
            // Texto da idade
            Expanded(
              child: Opacity(
                opacity: 0.8, // opacity-80
                child: Text(
                  '${strings.componentsStrings.age}: ${age?.toString() ?? '-'}',
                  style: const TextStyle(
                    fontSize: 12, // Details font
                    height: 21 / 12, // lineHeight = 21px
                    fontWeight: FontWeight.w400, // Regular
                    color: AppColors.black, // #111827
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Constrói um campo de texto individual
  Widget _buildTextField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String hintText,
    required List<TextInputFormatter> inputFormatters,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white, // #FFFFFF
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: AppColors.gray3, // border-gray-200
          width: 1,
        ),
      ),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        enabled: widget.enabled,
        keyboardType: TextInputType.number,
        inputFormatters: inputFormatters,
        textAlign: TextAlign.left, // Alinhado à esquerda como no Figma
        style: const TextStyle(
          fontSize: 14,
          height: 21 / 14, // lineHeight = 21px
          fontWeight: FontWeight.w400, // Regular
          color: AppColors.black, // #111827
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: const TextStyle(
            fontSize: 14,
            height: 21 / 14, // lineHeight = 21px
            fontWeight: FontWeight.w400, // Regular
            color: Color(0xFF929292), // #929292 - Neutral/Gray 6
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16, // px-4 = 16px
            vertical: 9,    // py-[9px] = 9px
          ),
        ),
      ),
    );
  }
}

/// Formatador para o campo de dia
class _DayInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) return newValue;
    
    final int? value = int.tryParse(newValue.text);
    if (value == null) return oldValue;
    
    if (value > 31) return oldValue;
    
    return newValue;
  }
}

/// Formatador para o campo de mês
class _MonthInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) return newValue;
    
    final int? value = int.tryParse(newValue.text);
    if (value == null) return oldValue;
    
    if (value > 12) return oldValue;
    
    return newValue;
  }
}

/// Formatador para o campo de ano
class _YearInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) return newValue;
    
    final int? value = int.tryParse(newValue.text);
    if (value == null) return oldValue;
    
    final currentYear = DateTime.now().year;
    if (newValue.text.length == 4 && (value < 1900 || value > currentYear)) {
      return oldValue;
    }
    
    return newValue;
  }
}
