import 'package:flutter/material.dart';
import '../../locator/locator.dart';
import '../../localization/repositories/locale_repository.dart';
import '../../theme/theme.dart';
import '../text_field/zuz_text_field_base.dart';
import 'zuz_date_picker.dart';

/// Campo de texto para seleção de datas que abre o ZuzDatePicker
/// 
/// Combina a aparência do ZuzTextField com a funcionalidade do ZuzDatePicker.
/// Ao clicar no campo, abre o modal do date picker para seleção.
class ZuzDatePickerField extends StatefulWidget {
  /// Label do campo
  final String label;
  
  /// Se o campo é opcional (mostra texto "Opcional")
  final bool isOptional;
  
  /// Tipo de seleção (única ou intervalo)
  final ZuzDatePickerType type;
  
  /// Placeholder quando não há data selecionada
  final String? placeholder;
  
  /// Texto de ajuda (helper text)
  final String? helperText;
  
  /// Mensagem de erro personalizada
  final String? errorText;
  
  /// Se o campo está habilitado
  final bool enabled;
  
  /// Data inicial selecionada (para tipo single)
  final DateTime? initialDate;
  
  /// Data de início do intervalo inicial (para tipo range)
  final DateTime? initialStartDate;
  
  /// Data final do intervalo inicial (para tipo range)
  final DateTime? initialEndDate;
  
  /// Data mínima selecionável
  final DateTime? firstDate;
  
  /// Data máxima selecionável
  final DateTime? lastDate;
  
  /// Callback chamado quando a seleção muda
  final ValueChanged<ZuzDatePickerResult>? onChanged;

  const ZuzDatePickerField({
    super.key,
    required this.label,
    this.isOptional = false,
    this.type = ZuzDatePickerType.single,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.enabled = true,
    this.initialDate,
    this.initialStartDate,
    this.initialEndDate,
    this.firstDate,
    this.lastDate,
    this.onChanged,
  });

  @override
  State<ZuzDatePickerField> createState() => _ZuzDatePickerFieldState();
}

class _ZuzDatePickerFieldState extends State<ZuzDatePickerField> {
  ZuzDatePickerResult? _result;
  bool _isFocused = false;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChanged);
    
    // Inicializar com os valores iniciais
    _result = ZuzDatePickerResult(
      selectedDate: widget.initialDate,
      startDate: widget.initialStartDate,
      endDate: widget.initialEndDate,
    );
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChanged);
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChanged() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  /// Formata uma data para exibição
  String _formatDate(DateTime? date) {
    if (date == null) return '';
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Formata período para exibição (MM/AAAA)
  String _formatPeriod(DateTime? date) {
    if (date == null) return '';
    return '${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Obtém o texto a ser exibido no campo
  String get _displayText {
    if (_result == null || !_result!.hasSelection) {
      return widget.placeholder ?? _getDefaultPlaceholder();
    }

    if (widget.type == ZuzDatePickerType.single) {
      return _formatDate(_result!.selectedDate);
    } else {
      // Para range, usar formato MM/AAAA - MM/AAAA como no Figma
      final start = _formatPeriod(_result!.startDate);
      final end = _formatPeriod(_result!.endDate);
      
      if (start.isNotEmpty && end.isNotEmpty) {
        return '$start - $end';
      } else if (start.isNotEmpty) {
        return start;
      } else if (end.isNotEmpty) {
        return end;
      }
      
      return widget.placeholder ?? _getDefaultPlaceholder();
    }
  }

  /// Obtém o placeholder padrão baseado no tipo
  String _getDefaultPlaceholder() {
    if (widget.type == ZuzDatePickerType.single) {
      return 'DD/MM/AAAA';
    } else {
      return 'MM/AAAA - MM/AAAA';
    }
  }

  /// Obtém a cor do texto baseada no estado
  Color get _textColor {
    if (!widget.enabled) {
      return AppColors.gray5;
    }
    
    if (_result == null || !_result!.hasSelection) {
      return AppColors.gray6; // Cor do placeholder
    }
    
    return AppColors.black; // Cor do texto normal
  }

  /// Abre o date picker
  Future<void> _openDatePicker() async {
    if (!widget.enabled) return;

    final result = await showZuzDatePicker(
      context: context,
      type: widget.type,
      initialDate: _result?.selectedDate,
      initialStartDate: _result?.startDate,
      initialEndDate: _result?.endDate,
      firstDate: widget.firstDate,
      lastDate: widget.lastDate,
    );

    if (result != null) {
      setState(() {
        _result = result;
      });
      widget.onChanged?.call(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    final strings = Locator.instance.get<LocaleRepository>().strings;
    
    return ZuzTextFieldBase(
      label: widget.label,
      isOptional: widget.isOptional,
      helperText: widget.helperText,
      errorText: widget.errorText,
      optionalText: strings.componentsStrings.optional,
      child: GestureDetector(
        onTap: _openDatePicker,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 9),
          decoration: BoxDecoration(
            color: widget.enabled ? AppColors.white : AppColors.gray1,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: _getBorderColor(),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // Texto do campo
              Expanded(
                child: Text(
                  _displayText,
                  style: TextStyle(
                    fontSize: 14,
                    height: 21 / 14, // lineHeight = 21px
                    fontWeight: FontWeight.w400,
                    color: _textColor,
                  ),
                ),
              ),
              
              // Ícone de calendário
              Icon(
                Icons.calendar_month,
                size: 20,
                color: widget.enabled ? AppColors.gray6 : AppColors.gray5,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Retorna a cor da borda baseada no estado
  Color _getBorderColor() {
    if (!widget.enabled) {
      return AppColors.gray3;
    }
    
    if (widget.errorText != null) {
      return AppColors.error;
    }
    
    if (_isFocused) {
      return AppColors.primary;
    }
    
    return AppColors.gray3;
  }
}
