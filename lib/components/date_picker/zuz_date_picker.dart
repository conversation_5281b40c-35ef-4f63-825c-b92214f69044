import 'package:flutter/material.dart';
import '../../locator/locator.dart';
import '../../localization/repositories/locale_repository.dart';
import '../../theme/theme.dart';
import '../button/button.dart';

/// Tipos de seleção do date picker
enum ZuzDatePickerType {
  /// Seleção de uma única data
  single,
  /// Seleção de intervalo de datas
  range,
}

/// Resultado da seleção do date picker
class ZuzDatePickerResult {
  /// Data única selecionada (quando type é single)
  final DateTime? selectedDate;
  
  /// Data de início do intervalo (quando type é range)
  final DateTime? startDate;
  
  /// Data final do intervalo (quando type é range)
  final DateTime? endDate;

  const ZuzDatePickerResult({
    this.selectedDate,
    this.startDate,
    this.endDate,
  });

  /// Verifica se há alguma data selecionada
  bool get hasSelection {
    return selectedDate != null || (startDate != null || endDate != null);
  }

  /// Limpa todas as seleções
  ZuzDatePickerResult clear() {
    return const ZuzDatePickerResult();
  }
}

/// Widget de calendário customizado para seleção de datas
class ZuzDatePicker extends StatefulWidget {
  /// Tipo de seleção (única ou intervalo)
  final ZuzDatePickerType type;
  
  /// Data inicial selecionada
  final DateTime? initialDate;
  
  /// Data de início do intervalo inicial
  final DateTime? initialStartDate;
  
  /// Data final do intervalo inicial
  final DateTime? initialEndDate;
  
  /// Callback chamado quando aplicar filtro
  final Function(ZuzDatePickerResult result)? onApply;
  
  /// Callback chamado quando fechar
  final VoidCallback? onClose;
  
  /// Data mínima selecionável
  final DateTime? firstDate;
  
  /// Data máxima selecionável
  final DateTime? lastDate;

  const ZuzDatePicker({
    super.key,
    required this.type,
    this.initialDate,
    this.initialStartDate,
    this.initialEndDate,
    this.onApply,
    this.onClose,
    this.firstDate,
    this.lastDate,
  });

  @override
  State<ZuzDatePicker> createState() => _ZuzDatePickerState();
}

class _ZuzDatePickerState extends State<ZuzDatePicker> {
  late DateTime? _selectedDate;
  late DateTime? _startDate;
  late DateTime? _endDate;
  late DateTime _currentMonth;
  
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate;
    _startDate = widget.initialStartDate;
    _endDate = widget.initialEndDate;
    _currentMonth = DateTime.now();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// Verifica se há alguma seleção ativa
  bool get _hasSelection {
    if (widget.type == ZuzDatePickerType.single) {
      return _selectedDate != null;
    } else {
      return _startDate != null || _endDate != null;
    }
  }

  /// Limpa todas as seleções
  void _clearSelection() {
    setState(() {
      _selectedDate = null;
      _startDate = null;
      _endDate = null;
    });
  }

  /// Aplicar filtro
  void _applyFilter() {
    final result = ZuzDatePickerResult(
      selectedDate: _selectedDate,
      startDate: _startDate,
      endDate: _endDate,
    );
    widget.onApply?.call(result);
  }

  /// Fechar modal
  void _close() {
    widget.onClose?.call();
  }

  /// Seleciona uma data
  void _selectDate(DateTime date) {
    setState(() {
      // Verificar se a data é de outro mês e navegar se necessário
      if (date.month != _currentMonth.month || date.year != _currentMonth.year) {
        _currentMonth = DateTime(date.year, date.month, 1);
      }
      
      // Lógica de seleção
      if (widget.type == ZuzDatePickerType.single) {
        _selectedDate = date;
      } else {
        // Lógica para seleção de intervalo
        if (_startDate == null || (_startDate != null && _endDate != null)) {
          // Primeira seleção ou resetar
          _startDate = date;
          _endDate = null;
        } else if (_endDate == null) {
          // Segunda seleção
          if (date.isBefore(_startDate!)) {
            _endDate = _startDate;
            _startDate = date;
          } else {
            _endDate = date;
          }
        }
      }
    });
  }

  /// Constrói um calendário
  Widget _buildCalendar({
    required DateTime displayedMonth,
    String? title,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
            ),
          ),
        ],
        _buildCalendarHeader(displayedMonth),
        _buildCalendarGrid(displayedMonth),
      ],
    );
  }

  /// Constrói o cabeçalho do calendário
  Widget _buildCalendarHeader(DateTime month) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () {
              setState(() {
                _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1, 1);
              });
            },
            icon: const Icon(Icons.chevron_left),
          ),
          Text(
            _getMonthYearText(month),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1, 1);
              });
            },
            icon: const Icon(Icons.chevron_right),
          ),
        ],
      ),
    );
  }

  /// Constrói a grade do calendário
  Widget _buildCalendarGrid(DateTime month) {
    final firstDayOfMonth = DateTime(month.year, month.month, 1);
    final lastDayOfMonth = DateTime(month.year, month.month + 1, 0);
    // Corrige o cálculo: domingo=0, segunda=1, ..., sábado=6
    final firstDayOfWeek = firstDayOfMonth.weekday == 7 ? 0 : firstDayOfMonth.weekday;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          // Cabeçalho dos dias da semana
          Row(
            children: Locator.instance.get<LocaleRepository>().strings.componentsStrings.weekDays
                .map((day) => Expanded(
                      child: AspectRatio(
                        aspectRatio: 1.0,
                        child: Container(
                          alignment: Alignment.center,
                          child: Text(
                            day,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: AppColors.gray5,
                            ),
                          ),
                        ),
                      ),
                    ))
                .toList(),
          ),
          
          // Dias organizados por semana
          ..._buildWeeksWithDividers(month, firstDayOfWeek, lastDayOfMonth),
        ],
      ),
    );
  }

  /// Constrói as semanas com divisórias entre elas
  List<Widget> _buildWeeksWithDividers(DateTime month, int firstDayOfWeek, DateTime lastDayOfMonth) {
    final weeks = <Widget>[];
    final totalDays = firstDayOfWeek + lastDayOfMonth.day;
    final numberOfWeeks = (totalDays / 7).ceil();
    
    // Dados do mês anterior e próximo para preencher espaços vazios
    final previousMonth = DateTime(month.year, month.month - 1, 1);
    final lastDayOfPreviousMonth = DateTime(month.year, month.month, 0);
    final nextMonth = DateTime(month.year, month.month + 1, 1);
    
    for (int week = 0; week < numberOfWeeks; week++) {
      final weekDays = <Widget>[];
      
      for (int dayOfWeek = 0; dayOfWeek < 7; dayOfWeek++) {
        final dayIndex = week * 7 + dayOfWeek;
        
        if (dayIndex < firstDayOfWeek) {
          // Dias do mês anterior
          final dayNumber = lastDayOfPreviousMonth.day - (firstDayOfWeek - dayIndex - 1);
          final date = DateTime(previousMonth.year, previousMonth.month, dayNumber);
          
          weekDays.add(
            Expanded(
              child: GestureDetector(
                onTap: () => _selectDate(date),
                child: AspectRatio(
                  aspectRatio: 1.0,
                  child: Container(
                    margin: const EdgeInsets.all(2),
                    alignment: Alignment.center,
                    child: Text(
                      dayNumber.toString(),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.gray6, // #929292
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        } else if (dayIndex >= firstDayOfWeek + lastDayOfMonth.day) {
          // Dias do próximo mês
          final dayNumber = dayIndex - (firstDayOfWeek + lastDayOfMonth.day) + 1;
          final date = DateTime(nextMonth.year, nextMonth.month, dayNumber);
          
          weekDays.add(
            Expanded(
              child: GestureDetector(
                onTap: () => _selectDate(date),
                child: AspectRatio(
                  aspectRatio: 1.0,
                  child: Container(
                    margin: const EdgeInsets.all(2),
                    alignment: Alignment.center,
                    child: Text(
                      dayNumber.toString(),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.gray6, // #929292
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        } else {
          // Dias do mês atual
          final dayNumber = dayIndex - firstDayOfWeek + 1;
          final date = DateTime(month.year, month.month, dayNumber);
          final isSelected = _isDateSelected(date);
          final isInRange = _isDateInRange(date);
          final isStart = _isStartDate(date);
          final isEnd = _isEndDate(date);
          final isToday = _isToday(date);
          
          weekDays.add(
            Expanded(
              child: GestureDetector(
                onTap: () => _selectDate(date),
                child: AspectRatio(
                  aspectRatio: 1.0,
                  child: Container(
                    margin: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: _getDateBackgroundColor(isSelected, isInRange, isStart, isEnd),
                      borderRadius: BorderRadius.circular(isToday && !(isSelected || isStart || isEnd) ? 20 : 8),
                      border: _getDateBorder(isSelected, isStart, isEnd, isToday),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      dayNumber.toString(),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: (isSelected || isStart || isEnd) ? FontWeight.w700 : FontWeight.w500,
                        color: _getDateTextColor(isSelected, isInRange, isStart, isEnd, isToday),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        }
      }
      
      // Adicionar a linha da semana
      weeks.add(
        Row(children: weekDays),
      );
      
      // Adicionar divisória entre as semanas (exceto na última semana)
      if (week < numberOfWeeks - 1) {
        weeks.add(
          const Divider(
            color: AppColors.gray3,
            height: 1,
            thickness: 1,
          ),
        );
      }
    }
    
    return weeks;
  }

  /// Verifica se uma data está selecionada
  bool _isDateSelected(DateTime date) {
    if (widget.type == ZuzDatePickerType.single) {
      return _selectedDate != null && _isSameDay(date, _selectedDate!);
    }
    return false;
  }

  /// Verifica se uma data está no intervalo selecionado
  bool _isDateInRange(DateTime date) {
    if (widget.type == ZuzDatePickerType.range && _startDate != null && _endDate != null) {
      return date.isAfter(_startDate!) && date.isBefore(_endDate!);
    }
    return false;
  }

  /// Verifica se é a data de início
  bool _isStartDate(DateTime date) {
    return _startDate != null && _isSameDay(date, _startDate!);
  }

  /// Verifica se é a data final
  bool _isEndDate(DateTime date) {
    return _endDate != null && _isSameDay(date, _endDate!);
  }

  /// Verifica se é hoje
  bool _isToday(DateTime date) {
    final today = DateTime.now();
    return _isSameDay(date, today);
  }

  /// Verifica se duas datas são do mesmo dia
  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  /// Obtém a cor de fundo da data
  Color _getDateBackgroundColor(bool isSelected, bool isInRange, bool isStart, bool isEnd) {
    if (isSelected || isStart || isEnd) {
      return AppColors.primary.withOpacity(0.5);
    } else if (isInRange) {
      return AppColors.primaryLight;
    }
    return Colors.transparent;
  }

  /// Obtém a borda da data
  Border? _getDateBorder(bool isSelected, bool isStart, bool isEnd, bool isToday) {
    if (isSelected || isStart || isEnd) {
      return Border.all(color: AppColors.white, width: 1);
    } else if (isToday) {
      return Border.all(color: AppColors.primary, width: 1);
    }
    return null;
  }

  /// Obtém a cor do texto da data
  Color _getDateTextColor(bool isSelected, bool isInRange, bool isStart, bool isEnd, bool isToday) {
    if (isSelected || isStart || isEnd) {
      return AppColors.black;
    } else if (isToday) {
      return AppColors.primary;
    } else if (isInRange) {
      return AppColors.primary;
    }
    return AppColors.black;
  }

  /// Obtém o texto do mês e ano
  String _getMonthYearText(DateTime date) {
    final months = Locator.instance.get<LocaleRepository>().strings.componentsStrings.months;
    return '${months[date.month - 1]} ${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppColors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 400,
          maxHeight: 600,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Calendários
            Flexible(
              child: SingleChildScrollView(
                child: _buildCalendar(displayedMonth: _currentMonth),
              ),
            ),
            
            // Botão remover filtro (se houver seleção)
            if (_hasSelection) ...[
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: TextButton(
                    onPressed: _clearSelection,
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.zero,
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: const Text(
                      'Remover Filtro',
                      style: TextStyle(
                        color: AppColors.error,
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        height: 1.5, // line-height: 21px / font-size: 14px = 1.5
                      ),
                    ),
                  ),
                ),
              ),
            ],
            
            // Botões de ação
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(
                    child: ZuzButton(
                      text: 'Fechar',
                      variant: ZuzButtonVariant.secondary,
                      onPressed: _close,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ZuzButton(
                      text: 'Aplicar Filtro',
                      variant: ZuzButtonVariant.primary,
                      onPressed: _applyFilter,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Função helper para mostrar o date picker como modal
Future<ZuzDatePickerResult?> showZuzDatePicker({
  required BuildContext context,
  required ZuzDatePickerType type,
  DateTime? initialDate,
  DateTime? initialStartDate,
  DateTime? initialEndDate,
  DateTime? firstDate,
  DateTime? lastDate,
}) {
  return showDialog<ZuzDatePickerResult>(
    context: context,
    barrierDismissible: true,
    builder: (context) => ZuzDatePicker(
      type: type,
      initialDate: initialDate,
      initialStartDate: initialStartDate,
      initialEndDate: initialEndDate,
      firstDate: firstDate,
      lastDate: lastDate,
      onApply: (result) => Navigator.of(context).pop(result),
      onClose: () => Navigator.of(context).pop(),
    ),
  );
}
