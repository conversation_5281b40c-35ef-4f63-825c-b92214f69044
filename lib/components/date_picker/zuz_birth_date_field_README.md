# ZuzBirthDateField

Widget para entrada de data de nascimento com campos separados (dia, mês, ano).

## Características

- **Campos separados**: DD, MM, AAAA em campos individuais
- **Dia opcional**: Permite configurar se o dia é obrigatório ou opcional
- **Cálculo automático de idade**: Mostra a idade calculada em tempo real
- **Validação inteligente**: <PERSON><PERSON> dias (1-31), meses (1-12) e anos (1900-atual)
- **Navegação automática**: Move o foco para o próximo campo automaticamente
- **Localização**: Usa o sistema de tradução para textos como "Dia opcional" e "Idade"

## Uso

### Básico (com dia opcional)
```dart
ZuzBirthDateField(
  label: 'Data de nascimento',
  isDayOptional: true,
  onChanged: (day, month, year) {
    print('Data: $day/$month/$year');
  },
)
```

### Com dia obrigatório
```dart
ZuzBirthDateField(
  label: 'Data de nascimento',
  isDayOptional: false,
  onChanged: (day, month, year) {
    // Processar mudança da data
  },
)
```

### Com valores iniciais
```dart
ZuzBirthDateField(
  label: 'Data de nascimento',
  initialDay: 15,
  initialMonth: 6,
  initialYear: 1990,
  onChanged: (day, month, year) {
    // Processar mudança da data
  },
)
```

## Parâmetros

### Obrigatórios
- `label`: String - Label do campo

### Opcionais
- `isDayOptional`: bool - Se o dia é opcional (padrão: true)
- `initialDay`: int? - Valor inicial do dia
- `initialMonth`: int? - Valor inicial do mês
- `initialYear`: int? - Valor inicial do ano
- `onChanged`: BirthDateChangedCallback? - Callback chamado quando a data muda
- `enabled`: bool - Se o campo está habilitado (padrão: true)

## Callback

```dart
typedef BirthDateChangedCallback = void Function(int? day, int? month, int? year);
```

O callback é chamado sempre que qualquer campo muda. Os valores podem ser `null` se:
- O campo estiver vazio
- O valor for inválido (ex: dia > 31, mês > 12)
- O ano for menor que 1900 ou maior que o ano atual

## Funcionalidades

### Cálculo de Idade
- Calcula automaticamente a idade baseada na data inserida
- Considera anos bissextos e datas futuras (retorna null para datas inválidas)
- Exibe com ícone de bolo e texto localizado

### Validação
- **Dia**: 1-31 (máximo 2 dígitos)
- **Mês**: 1-12 (máximo 2 dígitos)
- **Ano**: 1900 até ano atual (máximo 4 dígitos)

### Navegação
- Ao completar 2 dígitos no dia, move para o mês
- Ao completar 2 dígitos no mês, move para o ano
- Funciona apenas quando os valores são válidos

## Layout

- **Sempre mostra 3 campos**: DD, MM, AAAA (dia sempre visível)
- **isDayOptional controla**: Apenas se aparece o texto "Dia opcional" no canto superior direito
- **Campos têm proporções**: dia/mês (flex 1), ano (flex 2)

## Comportamento do Dia Opcional

- **isDayOptional = true**: Mostra "Dia opcional" e permite calcular idade mesmo sem dia
- **isDayOptional = false**: Não mostra texto adicional e exige dia preenchido para calcular idade
- **Layout visual**: Sempre 3 campos independente da configuração

## Localização

Utiliza as seguintes strings do sistema:
- `componentsStrings.dayOptional`: "Dia opcional"
- `componentsStrings.age`: "Idade"

## Observações

- Segue o Design System Zuz com cores e estilos consistentes
- Usa formatadores de entrada para garantir apenas números válidos
- Permite entrada via teclado numérico
- Campos são centralizados para melhor experiência visual
