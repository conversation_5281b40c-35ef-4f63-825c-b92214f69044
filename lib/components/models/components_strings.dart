/// Interface de mensagens i18n para o domínio de componentes.
abstract class ComponentsStrings {
  /// Texto exibido em campos opcionais
  String get optional;
  
  /// Texto para "Dia opcional" em campos de data de nascimento
  String get dayOptional;
  
  /// Texto para "Idade" em campos de data de nascimento
  String get age;
  
  /// Nomes dos dias da semana (abreviados) para o date picker
  String get sunday;
  String get monday;
  String get tuesday;
  String get wednesday;
  String get thursday;
  String get friday;
  String get saturday;
  
  /// Nomes dos meses para o date picker
  String get january;
  String get february;
  String get march;
  String get april;
  String get may;
  String get june;
  String get july;
  String get august;
  String get september;
  String get october;
  String get november;
  String get december;
  
  /// Lista dos dias da semana em ordem (domingo a sábado)
  List<String> get weekDays => [
    sunday,
    monday,
    tuesday,
    wednesday,
    thursday,
    friday,
    saturday,
  ];

  /// Rótulo para o campo de status em components
  String get status;
  
  /// Lista dos meses em ordem (janeiro a dezembro)
  List<String> get months => [
    january,
    february,
    march,
    april,
    may,
    june,
    july,
    august,
    september,
    october,
    november,
    december,
  ];
}
