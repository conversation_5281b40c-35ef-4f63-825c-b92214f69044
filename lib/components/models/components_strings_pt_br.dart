import 'components_strings.dart';

/// Implementação `pt_BR` de mensagens do domínio de componentes.
class ComponentsStringsPtBr implements ComponentsStrings {
  @override
  String get optional => 'Opcional';

  @override
  String get dayOptional => 'Dia opcional';

  @override
  String get age => 'Idade';

  @override
  String get sunday => 'D';

  @override
  String get monday => 'S';

  @override
  String get tuesday => 'T';

  @override
  String get wednesday => 'Q';

  @override
  String get thursday => 'Q';

  @override
  String get friday => 'S';

  @override
  String get saturday => 'S';

  @override
  String get january => 'Janeiro';

  @override
  String get february => 'Fevereiro';

  @override
  String get march => 'Março';

  @override
  String get april => 'Abril';

  @override
  String get may => 'Maio';

  @override
  String get june => 'Junho';

  @override
  String get july => 'Julho';

  @override
  String get august => 'Agosto';

  @override
  String get september => 'Setembro';

  @override
  String get october => 'Outubro';

  @override
  String get november => 'Novembro';

  @override
  String get december => 'Dezembro';

  @override
  List<String> get weekDays => [
    sunday,
    monday,
    tuesday,
    wednesday,
    thursday,
    friday,
    saturday,
  ];

  @override
  List<String> get months => [
    january,
    february,
    march,
    april,
    may,
    june,
    july,
    august,
    september,
    october,
    november,
    december,
  ];

  @override
  String get status => 'Status';
}
