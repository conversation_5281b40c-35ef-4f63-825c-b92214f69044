# ZuzDropdown

## Descrição
Componente de dropdown personalizado do Design System Zuz que suporta seleção única e múltipla, seguindo os padrões visuais e de usabilidade do aplicativo.

## Características
- **Seleção única e múltipla**: Configurável através da propriedade `isMultiSelect`
- **Design System**: Segue o padrão visual do aplicativo com cores do `AppColors`
- **Validação**: Integração com `Form` através do `validator`
- **Acessibilidade**: Tooltips para textos longos e navegação por foco
- **Responsivo**: Adaptação automática da largura para textos longos
- **Overlay customizado**: Lista de opções sobreposta com controle de posicionamento

## Funcionalidades de Texto Longo
- **Lista de opções**: Altura dinâmica que se adapta automaticamente ao conteúdo, permitindo até 2 linhas de texto
- **Dropdown selecionado**: Altura calculada dinamicamente baseada no texto selecionado (até 2 linhas)
- **Quebra de linha inteligente**: Textos longos quebram naturalmente em até 2 linhas com reticências se necessário
- **Alinhamento otimizado**: Ícones e checkboxes se alinham corretamente com textos multilinhas
- **Altura mínima preservada**: Mantém 39px como altura mínima para consistência visual

## Propriedades Principais

### Básicas
- `options`: Lista de `ZuzDropdownOption<T>` com as opções disponíveis
- `label`: Rótulo do campo (opcional)
- `placeholder`: Texto exibido quando nada está selecionado
- `isOptional`: Indica se o campo é opcional
- `enabled`: Controla se o campo está habilitado

### Seleção
- `isMultiSelect`: Define se permite múltipla seleção
- `selectedValue`: Valor selecionado (seleção única)
- `selectedValues`: Lista de valores selecionados (múltipla seleção)
- `onChanged`: Callback para seleção única
- `onMultipleChanged`: Callback para múltipla seleção

### Validação e Feedback
- `validator`: Função de validação para uso com `Form`
- `errorText`: Texto de erro personalizado
- `helperText`: Texto de ajuda

### Ícones
- `prefixIcon`: Ícone à esquerda
- `suffixIcon`: Ícone à direita personalizado
- `onSuffixIconPressed`: Callback do ícone à direita

## Exemplo de Uso

### Seleção Única
```dart
ZuzDropdown<String>(
  label: 'Categoria',
  options: [
    ZuzDropdownOption(value: 'tech', label: 'Tecnologia'),
    ZuzDropdownOption(value: 'business', label: 'Negócios'),
  ],
  selectedValue: selectedCategory,
  onChanged: (value) => setState(() => selectedCategory = value),
  placeholder: 'Selecione uma categoria',
)
```

### Seleção Múltipla
```dart
ZuzDropdown<String>(
  isMultiSelect: true,
  label: 'Tags',
  options: tagOptions,
  selectedValues: selectedTags,
  onMultipleChanged: (values) => setState(() => selectedTags = values),
  placeholder: 'Selecione as tags',
)
```

### Com Validação
```dart
ZuzDropdown<String>(
  label: 'Categoria Obrigatória',
  options: categoryOptions,
  selectedValue: category,
  onChanged: (value) => setState(() => category = value),
  validator: (value) => value == null ? 'Campo obrigatório' : null,
)
```

## Estrutura de Arquivos
- `zuz_dropdown.dart`: Componente principal
- `README.md`: Documentação do componente

## Dependências
- `ZuzTextFieldBase`: Base visual dos campos de entrada
- `ZuzCheckBox`: Checkbox para seleção múltipla
- `AppColors`: Cores do Design System
- `LocaleRepository`: Sistema de localização

## Melhorias Futuras
- [ ] Suporte a busca/filtro nas opções
- [ ] Paginação para listas muito grandes
- [ ] Suporte a grupos de opções
- [ ] Configuração de altura máxima customizável para as opções
- [ ] Suporte a ícones nas opções
- [ ] Modo de seleção por chips externos ao dropdown
- [ ] Configuração do número máximo de linhas (atualmente limitado a 2)
