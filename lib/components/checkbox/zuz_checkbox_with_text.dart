import 'package:flutter/material.dart';

import 'zuz_checkbox.dart';

class ZuzCheckboxWithText extends StatelessWidget {
  const ZuzCheckboxWithText({
    super.key,
    required this.value,
    required this.onChanged,
    required this.text,
  });

  final bool? value;
  final ValueChanged<bool?>? onChanged;
  final String text;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        ZuzCheckBox(value: value, onChanged: onChanged),
        const SizedBox(width: 8),
        Flexible(
          child: GestureDetector(
            onTap: onChanged != null ? () => onChanged!(!value!) : null,
            child: Text(text),
          ),
        ),
      ],
    );
  }
}
