import 'package:flutter/material.dart';
import 'zuz_box_checkbox.dart';
import 'zuz_box_radiobutton.dart';

/// Enum para definir o tipo de seleção do grupo
enum ZuzGroupBoxType {
  checkbox,
  radioButton,
}

/// Enum para definir o layout do grupo
enum ZuzGroupBoxLayout {
  /// Um item por linha
  singleColumn,
  /// Dois itens por linha
  doubleColumn,
  /// Layout responsivo (wrap)
  wrap,
}

/// Item individual do grupo
class ZuzGroupBoxItem<T> {
  /// Texto do item
  final String text;
  
  /// Texto de ajuda que aparece abaixo do item (opcional)
  final String? helperText;
  
  /// Valor do item (usado para radio buttons)
  final T value;
  
  /// Se o campo é opcional (mostra texto "Opcional")
  final bool isOptional;

  const ZuzGroupBoxItem({
    required this.text,
    this.helperText,
    required this.value,
    this.isOptional = false,
  });
}

/// Widget de grupo que gera múltiplos ZuzBoxCheckbox ou ZuzBoxRadioButton
/// 
/// Permite criar grupos de checkboxes ou radio buttons com layout flexível.
/// Apenas o primeiro item recebe o label quando especificado.
class ZuzGroupBox<T> extends StatelessWidget {
  /// Label do grupo (opcional, aplicado apenas ao primeiro item)
  final String? label;
  
  /// Lista de itens do grupo
  final List<ZuzGroupBoxItem<T>> items;
  
  /// Tipo de seleção (checkbox ou radio button)
  final ZuzGroupBoxType type;
  
  /// Layout do grupo
  final ZuzGroupBoxLayout layout;
  
  /// Valores selecionados (para checkboxes - lista de valores)
  final List<T>? selectedValues;
  
  /// Valor selecionado (para radio buttons - valor único)
  final T? selectedValue;
  
  /// Callback para checkboxes - recebe lista de valores selecionados
  final ValueChanged<List<T>>? onCheckboxChanged;
  
  /// Callback para radio buttons - recebe valor selecionado
  final ValueChanged<T?>? onRadioChanged;
  
  /// Espaçamento entre itens
  final double spacing;

  const ZuzGroupBox({
    super.key,
    this.label,
    required this.items,
    required this.type,
    this.layout = ZuzGroupBoxLayout.singleColumn,
    this.selectedValues,
    this.selectedValue,
    this.onCheckboxChanged,
    this.onRadioChanged,
    this.spacing = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    if (items.isEmpty) return const SizedBox.shrink();

    switch (layout) {
      case ZuzGroupBoxLayout.singleColumn:
        return _buildSingleColumn();
      case ZuzGroupBoxLayout.doubleColumn:
        return _buildDoubleColumn();
      case ZuzGroupBoxLayout.wrap:
        return _buildWrap();
    }
  }

  /// Constrói layout de uma coluna
  Widget _buildSingleColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < items.length; i++) ...[
          _buildItem(items[i], i),
          if (i < items.length - 1) SizedBox(height: spacing),
        ],
      ],
    );
  }

  /// Constrói layout de duas colunas
  Widget _buildDoubleColumn() {
    final List<Widget> rows = [];
    
    for (int i = 0; i < items.length; i += 2) {
      final bool hasSecondItem = i + 1 < items.length;
      
      rows.add(
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Expanded(child: _buildItem(items[i], i)),
            if (hasSecondItem) ...[
              SizedBox(width: spacing),
              Expanded(child: _buildItem(items[i + 1], i + 1)),
            ] else
              const Expanded(child: SizedBox.shrink()),
          ],
        ),
      );
      
      if (i + 2 < items.length) {
        rows.add(SizedBox(height: spacing));
      }
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: rows,
    );
  }

  /// Constrói layout responsivo (wrap)
  Widget _buildWrap() {
    return Wrap(
      spacing: spacing,
      runSpacing: spacing,
      children: [
        for (int i = 0; i < items.length; i++)
          _buildItem(items[i], i),
      ],
    );
  }

  /// Constrói um item individual
  Widget _buildItem(ZuzGroupBoxItem<T> item, int index) {
    final String itemLabel = (index == 0 && label != null) ? label! : '';
    
    if (type == ZuzGroupBoxType.checkbox) {
      return ZuzBoxCheckbox(
        label: itemLabel,
        text: item.text,
        helperText: item.helperText,
        value: selectedValues?.contains(item.value) ?? false,
        onChanged: _handleCheckboxChanged(item.value),
        isOptional: item.isOptional,
      );
    } else {
      return ZuzBoxRadioButton<T>(
        label: itemLabel,
        text: item.text,
        helperText: item.helperText,
        value: item.value,
        groupValue: selectedValue,
        onChanged: onRadioChanged,
        isOptional: item.isOptional,
      );
    }
  }

  /// Manipula mudanças em checkboxes
  ValueChanged<bool?>? _handleCheckboxChanged(T value) {
    if (onCheckboxChanged == null) return null;
    
    return (bool? isSelected) {
      final List<T> currentValues = List<T>.from(selectedValues ?? []);
      
      if (isSelected == true) {
        if (!currentValues.contains(value)) {
          currentValues.add(value);
        }
      } else {
        currentValues.remove(value);
      }
      
      onCheckboxChanged!(currentValues);
    };
  }
}
