import 'package:flutter/material.dart';
import '../../theme/theme.dart';
import 'zuz_box_base.dart';
import 'zuz_radiobutton.dart';

/// Radio button com container e texto seguindo o Design System
/// 
/// Widget que combina um ZuzRadioButton com um container estilizado,
/// label e texto descritivo. Permite clicar em qualquer área do
/// container para alterar o estado do radio button.
class ZuzBoxRadioButton<T> extends StatelessWidget {
  /// Label do radio button (mesmo formato do text field)
  final String label;
  
  /// Texto descritivo ao lado do radio button
  final String text;
  
  /// Texto de ajuda que aparece abaixo do radio button (opcional)
  final String? helperText;
  
  /// Valor desta opção
  final T value;
  
  /// Valor do grupo (qual opção está selecionada)
  final T? groupValue;
  
  /// Callback chamado quando o valor muda
  final ValueChanged<T?>? onChanged;
  
  /// Se o campo é opcional (mostra texto "Opcional")
  final bool isOptional;

  const ZuzBoxRadioButton({
    super.key,
    required this.label,
    required this.text,
    this.helperText,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    this.isOptional = false,
  });

  @override
  Widget build(BuildContext context) {
    final bool isSelected = value == groupValue;
    
    return ZuzBoxBase(
      label: label,
      text: text,
      helperText: helperText,
      isSelected: isSelected,
      isOptional: isOptional,
      selectedBorderColor: AppColors.primary, // Cor primária para radio button
      selectedBackgroundColor: AppColors.primaryLight, // Background primary light quando selecionado
      onTap: onChanged != null ? () => onChanged!(value) : null,
      selectionWidget: ZuzRadioButton<T>(
        value: value,
        groupValue: groupValue,
        onChanged: onChanged,
      ),
    );
  }
}
