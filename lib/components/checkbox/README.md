# ZuzGroupBox

Widget de grupo que gera múltiplos `ZuzBoxCheckbox` ou `ZuzBoxRadioButton` com layout flexível.

## Características

- **Tipo de seleção configurável**: Pode ser checkbox ou radio button
- **Layout flexível**: Suporta uma coluna, duas colunas ou layout responsivo (wrap)
- **Label inteligente**: Apenas o primeiro item recebe o label quando especificado
- **Gerenciamento de estado**: Controla automaticamente o estado do grupo para radio buttons
- **Espaçamento configurável**: Permite ajustar o espaçamento entre itens

## Uso

### Grupo de Checkboxes (2 colunas)
```dart
ZuzGroupBox<String>(
  label: 'Sexo',
  type: ZuzGroupBoxType.checkbox,
  layout: ZuzGroupBoxLayout.doubleColumn,
  items: const [
    ZuzGroupBoxItem(text: 'Macho', value: 'male'),
    ZuzGroupBoxItem(text: 'Fêmea', value: 'female'),
  ],
  selectedValues: selectedGenders,
  onCheckboxChanged: (values) => setState(() => selectedGenders = values),
)
```

### Grupo de Radio Buttons com Helper Text
```dart
ZuzGroupBox<String>(
  label: 'Permissão',
  type: ZuzGroupBoxType.radioButton,
  layout: ZuzGroupBoxLayout.singleColumn,
  items: const [
    ZuzGroupBoxItem(
      text: 'Gestor',
      helperText: 'Possui funcionalidades de gerenciamento: cadastros, edições, exclusões e compra de créditos.',
      value: 'gestor',
    ),
    ZuzGroupBoxItem(
      text: 'Colaborador',
      helperText: 'Acesso limitado: apenas solicita avaliações e visualiza informações.',
      value: 'colaborador',
    ),
  ],
  selectedValue: selectedPermission,
  onRadioChanged: (value) => setState(() => selectedPermission = value),
)
```

## Estrutura de Item

```dart
ZuzGroupBoxItem<T>(
  text: 'Texto do item',
  helperText: 'Texto de ajuda opcional que aparece abaixo', // Novo!
  value: valorDoItem,
  isOptional: false, // opcional
)
```

## Helper Text

- **Novo parâmetro** `helperText` opcional em `ZuzGroupBoxItem`
- Aparece abaixo do checkbox/radiobutton dentro do container
- Usa fonte Details (12px Regular, lineHeight 21px)
- Cor: `#111827` (AppColors.black)
- Espaçamento: 8px acima do helper text

## Observações

- Para checkboxes, use `selectedValues` (lista) e `onCheckboxChanged`
- Para radio buttons, use `selectedValue` (valor único) e `onRadioChanged`
- O label é aplicado apenas ao primeiro item quando especificado
- Em layouts de duas colunas, se houver número ímpar de itens, o último ficará sozinho na linha
- O widget segue o Design System Zuz com cores e estilos consistentes
