import 'package:flutter/material.dart';
import '../../theme/theme.dart';

/// Widget base para containers de seleção (checkbox/radio) seguindo o Design System
/// 
/// Widget reutilizável que contém o label e o container estilizado,
/// permitindo diferentes tipos de widgets de seleção como filhos.
class ZuzBoxBase extends StatelessWidget {
  /// Label do campo (mesmo formato do text field)
  final String label;
  
  /// Texto descritivo ao lado do widget de seleção
  final String text;
  
  /// Texto de ajuda que aparece abaixo do container (opcional)
  final String? helperText;
  
  /// Widget de seleção (checkbox, radio, etc.)
  final Widget selectionWidget;
  
  /// Se está selecionado (para determinar a cor da borda)
  final bool isSelected;
  
  /// Callback chamado quando clica no container
  final VoidCallback? onTap;
  
  /// Se o campo é opcional (mostra texto "Opcional")
  final bool isOptional;
  
  /// Cor da borda quando selecionado (padrão: success)
  final Color? selectedBorderColor;
  
  /// Cor do background quando selecionado (padrão: branco)
  final Color? selectedBackgroundColor;

  const ZuzBoxBase({
    super.key,
    required this.label,
    required this.text,
    this.helperText,
    required this.selectionWidget,
    required this.isSelected,
    this.onTap,
    this.isOptional = false,
    this.selectedBorderColor,
    this.selectedBackgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final bool hasLabel = label.isNotEmpty;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Row com label e indicação de opcional (apenas se houver label)
        if (hasLabel) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label principal
              Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  height: 21 / 14, // lineHeight = 21px
                  fontWeight: FontWeight.w700, // FontWeight(700)
                  color: AppColors.black,
                ),
              ),
              
              // Texto "Opcional" se necessário
              if (isOptional)
                const Text(
                  "Opcional",
                  style: TextStyle(
                    fontSize: 14,
                    height: 21 / 14, // lineHeight = 21px
                    fontWeight: FontWeight.w400, // FontWeight(400)
                    color: AppColors.gray5,
                  ),
                  textAlign: TextAlign.right,
                ),
            ],
          ),
          
          const SizedBox(height: 4), // Espaçamento de 4px
        ],
        
        // Container principal clicável
        GestureDetector(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.fromLTRB(16, 12, 16, 12), // py-3 = 12px conforme Figma
            decoration: BoxDecoration(
              color: _getBackgroundColor(),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: _getBorderColor(),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Row com checkbox/radio e texto
                Row(
                  children: [
                    // Widget de seleção (checkbox, radio, etc.)
                    selectionWidget,
                    
                    const SizedBox(width: 8), // Gap de 8px
                    
                    // Texto descritivo
                    Expanded(
                      child: Text(
                        text,
                        style: const TextStyle(
                          fontSize: 14,
                          height: 21 / 14, // lineHeight = 21px
                          fontWeight: FontWeight.w400,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                  ],
                ),
                
                // Helper text (se existir)
                if (helperText != null && helperText!.isNotEmpty) ...[
                  const SizedBox(height: 8), // gap-2 = 8px
                  Text(
                    helperText!,
                    style: const TextStyle(
                      fontSize: 12, // Details font
                      height: 21 / 12, // lineHeight = 21px
                      fontWeight: FontWeight.w400, // Regular
                      color: AppColors.black, // #111827
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }
  
  /// Retorna a cor da borda do container baseada no estado de seleção
  Color _getBorderColor() {
    if (isSelected) {
      return selectedBorderColor ?? AppColors.success; // Usa cor personalizada ou padrão (success)
    }
    return AppColors.gray3; // Cinza padrão
  }
  
  /// Retorna a cor do background do container baseada no estado de seleção
  Color _getBackgroundColor() {
    if (isSelected && selectedBackgroundColor != null) {
      return selectedBackgroundColor!; // Usa cor personalizada quando selecionado
    }
    return AppColors.white; // Branco padrão
  }
}
