import 'package:flutter/material.dart';
import 'zuz_box_base.dart';
import 'zuz_checkbox.dart';

/// Checkbox com container e texto seguindo o Design System
/// 
/// Widget que combina um ZuzCheckBox com um container estilizado,
/// label e texto descritivo. Permite clicar em qualquer área do
/// container para alterar o estado do checkbox.
class ZuzBoxCheckbox extends StatelessWidget {
  /// Label do checkbox (mesmo formato do text field)
  final String label;
  
  /// Texto descritivo ao lado do checkbox
  final String text;
  
  /// Texto de ajuda que aparece abaixo do checkbox (opcional)
  final String? helperText;
  
  /// Valor atual do checkbox (true, false ou null para indeterminate)
  final bool? value;
  
  /// Callback chamado quando o valor muda
  final ValueChanged<bool?>? onChanged;
  
  /// Se está no estado tristate (permite valor null)
  final bool tristate;
  
  /// Se o campo é opcional (mostra texto "Opcional")
  final bool isOptional;

  const ZuzBoxCheckbox({
    super.key,
    required this.label,
    required this.text,
    this.helperText,
    required this.value,
    required this.onChanged,
    this.tristate = false,
    this.isOptional = false,
  });

  @override
  Widget build(BuildContext context) {
    return ZuzBoxBase(
      label: label,
      text: text,
      helperText: helperText,
      isSelected: value == true,
      isOptional: isOptional,
      onTap: onChanged != null ? _handleTap : null,
      selectionWidget: ZuzCheckBox(
        value: value,
        onChanged: onChanged,
        tristate: tristate,
      ),
    );
  }
  
  /// Manipula o clique no container
  void _handleTap() {
    if (onChanged == null) return;
    
    if (tristate) {
      // Ciclo: false -> true -> null -> false
      if (value == false) {
        onChanged!(true);
      } else if (value == true) {
        onChanged!(null);
      } else {
        onChanged!(false);
      }
    } else {
      // Ciclo binário: false <-> true
      onChanged!(!(value ?? false));
    }
  }
}
