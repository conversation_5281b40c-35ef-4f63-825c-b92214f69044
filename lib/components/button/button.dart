import 'package:flutter/material.dart';
import '../../theme/theme.dart';

/// Estados do botão customizado
enum AppButtonState {
  normal,
  hover,
  pressed,
  disabled,
  loading,
}

/// Variantes disponíveis do botão
enum ZuzButtonVariant {
  primary,
  secondary,
  tertiary,
  destructive,
  icon,
  iconDestructive,
}

/// Botão customizado do aplicativo seguindo o Design System
class ZuzButton extends StatefulWidget {
  /// Texto do botão
  final String text;
  
  /// Callback quando o botão é pressionado
  final VoidCallback? onPressed;
  
  /// Se o botão está em estado de loading
  final bool isLoading;
  
  /// Ícone opcional do botão
  final IconData? icon;
  
  /// Largura do botão (null para ocupar o espaço disponível)
  final double? width;
  
  /// Altura do botão
  final double? height;
  
  /// Estilo do texto customizado
  final TextStyle? textStyle;

  /// Variante do botão (padrão: primary)
  final ZuzButtonVariant variant;

  /// Número máximo de linhas do texto
  final int? maxLines;

  const ZuzButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.icon,
    this.width,
    this.height,
    this.textStyle,
    this.variant = ZuzButtonVariant.primary,
    this.maxLines,
  });

  @override
  State<ZuzButton> createState() => _ZuzButtonState();
}

class _ZuzButtonState extends State<ZuzButton> {
  bool _isHovered = false;
  bool _isPressed = false;
  bool _isFocused = false;

  // Constantes reutilizáveis (não alteram comportamento visual)
  static const double _kDefaultHeight = 48.0; // altura padrão (não icon / não tertiary)
  static const double _kPaddingV = 14.0; // padding vertical padrão
  static const double _kPaddingH = 20.0; // padding horizontal padrão
  static const double _kIconSize = 16.0; // tamanho padrão do ícone
  static const Duration _kAnimDuration = Duration(milliseconds: 150); // duração da animação

  // Getters semânticos para reduzir repetição de condições
  bool get _isIconVariant => widget.variant == ZuzButtonVariant.icon || widget.variant == ZuzButtonVariant.iconDestructive;
  bool get _isTertiary => widget.variant == ZuzButtonVariant.tertiary;
  bool get _showsText => !(_isIconVariant || (_isTertiary && widget.isLoading));
  bool get _needsLoaderGap => !_isTertiary && !_isIconVariant; // mantém lógica original de gap após loader

  // Altura efetiva (replica expressão inline original)
  double? get _effectiveHeight => widget.height ?? (_isIconVariant || _isTertiary ? null : _kDefaultHeight);

  AppButtonState get _currentState {
    if (widget.isLoading) return AppButtonState.loading;
    if (widget.onPressed == null) return AppButtonState.disabled;
    if (_isPressed || _isFocused) return AppButtonState.pressed;
    if (_isHovered) return AppButtonState.hover;
    return AppButtonState.normal;
  }

  Color get _backgroundColor {
    switch (widget.variant) {
      case ZuzButtonVariant.primary:
        switch (_currentState) {
          case AppButtonState.normal:
          case AppButtonState.loading:
          case AppButtonState.hover:
            return AppColors.primary;
          case AppButtonState.pressed:
            return AppColors.primaryPressed;
          case AppButtonState.disabled:
            return AppColors.gray6;
        }
      case ZuzButtonVariant.secondary:
        switch (_currentState) {
          case AppButtonState.normal:
          case AppButtonState.hover:
          case AppButtonState.loading:
            return AppColors.white;
          case AppButtonState.pressed:
            return AppColors.gray1;
          case AppButtonState.disabled:
            return AppColors.gray6;
        }
      case ZuzButtonVariant.tertiary:
        // Tertiary: texto-only, sem fundo
        return Colors.transparent;
      case ZuzButtonVariant.destructive:
        switch (_currentState) {
          case AppButtonState.normal:
          case AppButtonState.loading:
          case AppButtonState.hover:
            return AppColors.error;
          case AppButtonState.pressed:
            return AppColors.errorTexts;
          case AppButtonState.disabled:
            return AppColors.gray6;
        }
      case ZuzButtonVariant.icon:
        // Icon (normal)
        switch (_currentState) {
          case AppButtonState.normal:
          case AppButtonState.loading:
            return AppColors.white;
          case AppButtonState.hover:
            return AppColors.gray0;
          case AppButtonState.pressed:
            return AppColors.gray3;
          case AppButtonState.disabled:
            return AppColors.white;
        }
      case ZuzButtonVariant.iconDestructive:
        // Icon (destructive)
        switch (_currentState) {
          case AppButtonState.normal:
          case AppButtonState.loading:
            return AppColors.white;
          case AppButtonState.hover:
          case AppButtonState.pressed:
            return AppColors.errorBoxes;
          case AppButtonState.disabled:
            return AppColors.white;
        }
    }
  }

  List<BoxShadow>? get _boxShadow {
    // Icon variants têm sombra em hover (ambos) e pressed (apenas destructive)
    final isHover = _currentState == AppButtonState.hover;
    final isPressed = _currentState == AppButtonState.pressed;
    switch (widget.variant) {
      case ZuzButtonVariant.primary:
      case ZuzButtonVariant.secondary:
        if (!isHover) return null;
        return [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.05),
            offset: Offset(0, 1),
            blurRadius: 2,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: AppColors.white,
            offset: Offset(0, 0),
            blurRadius: 0,
            spreadRadius: 2,
          ),
          BoxShadow(
            color: AppColors.primary,
            offset: Offset(0, 0),
            blurRadius: 0,
            spreadRadius: 4,
          ),
        ];
      case ZuzButtonVariant.destructive:
        if (!isHover) return null;
        return [
          BoxShadow(
            color: AppColors.black.withOpacity(0.05),
            offset: Offset(0, 1),
            blurRadius: 2,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: AppColors.white,
            offset: Offset(0, 0),
            blurRadius: 0,
            spreadRadius: 2,
          ),
          BoxShadow(
            color: AppColors.error,
            offset: Offset(0, 0),
            blurRadius: 0,
            spreadRadius: 4,
          ),
        ];
      case ZuzButtonVariant.tertiary:
        return null;
      case ZuzButtonVariant.icon:
        if (!(isHover)) return null;
        // Sombra drop para hover normal
        return const [
          BoxShadow(
            color: AppColors.gray2,
            offset: Offset(0, 12),
            blurRadius: 24,
            spreadRadius: 0,
          ),
        ];
      case ZuzButtonVariant.iconDestructive:
        if (!(isHover || isPressed)) return null;
        // Sombra drop para hover e pressed destructive
        return const [
          BoxShadow(
            color: AppColors.gray2,
            offset: Offset(0, 12),
            blurRadius: 24,
            spreadRadius: 0,
          ),
        ];
    }
  }

  Color get _textColor {
    switch (widget.variant) {
      case ZuzButtonVariant.primary:
        // Disabled no DS: branco
  return AppColors.white; // ambos os ramos devolviam branco
      case ZuzButtonVariant.secondary:
        if (_currentState == AppButtonState.disabled) {
          return AppColors.white;
        }
        // Default/Hover/Pressed/Loading: Brand Primary
        return AppColors.primary;
      case ZuzButtonVariant.tertiary:
        // Default/Hover: brand primary; Pressed: primary pressed; Disabled: gray6
        if (_currentState == AppButtonState.disabled) {
          return AppColors.gray6;
        }
        if (_currentState == AppButtonState.pressed) {
          return AppColors.primaryPressed;
        }
        return AppColors.primary;
      case ZuzButtonVariant.destructive:
      case ZuzButtonVariant.icon:
        // Para Icon normal, usamos cor do ícone (black). Para destructive (separado abaixo)
        if (widget.variant == ZuzButtonVariant.icon) {
          return _currentState == AppButtonState.disabled
              ? AppColors.gray6
              : AppColors.black;
        }
        return AppColors.white;
      case ZuzButtonVariant.iconDestructive:
        // Ícone vermelho nos estados, disabled cinza-6
        return _currentState == AppButtonState.disabled
            ? AppColors.gray6
            : AppColors.error;
    }
  }

  TextStyle get _effectiveTextStyle {
    // Estilo base vindo do caller ou vazio
    final base = widget.textStyle ?? const TextStyle();
    switch (widget.variant) {
      case ZuzButtonVariant.secondary:
        // Body Bold 14px, 700, 21px line-height (1.5), Century Gothic
        return base.copyWith(
          fontSize: 14,
          fontWeight: FontWeight.w700,
          height: 1.5,
          color: _textColor,
          shadows: _textShadows,
        );
      case ZuzButtonVariant.primary:
        // Primary: mesma tipografia do DS, cor branca
        return base.copyWith(
          fontSize: 14,
          fontWeight: FontWeight.w700,
          height: 1.5,
          color: _textColor,
        );
      case ZuzButtonVariant.tertiary:
      case ZuzButtonVariant.destructive:
  case ZuzButtonVariant.icon:
  case ZuzButtonVariant.iconDestructive:
        // placeholders até implementar variantes
        return base.copyWith(
          fontSize: 14,
          fontWeight: FontWeight.w700,
          height: 1.5,
          color: _textColor,
          shadows: _textShadows,
        );
    }
  }

  List<Shadow>? get _textShadows {
    // No DS: apenas Tertiary em hover tem text-shadow
    if (widget.variant == ZuzButtonVariant.tertiary && _currentState == AppButtonState.hover) {
      return [
        Shadow(
          color: AppColors.primary.withOpacity(0.5),
          blurRadius: 8,
          offset: Offset(0, 0),
        ),
      ];
    }
    // Secondary pede sombra apenas no hover? DS indica sombra apenas para Hover da variant secondary? não; já aplicado no boxShadow
    if (widget.variant == ZuzButtonVariant.secondary && _currentState == AppButtonState.hover) {
      return null; // sombra já é via boxShadow (focus ring)
    }
    return null;
  }

  Widget? _buildIcon() {
    if (widget.icon == null) return null;
    // Para Tertiary em hover, usamos Text com glyph para suportar shadow no ícone
    if (widget.variant == ZuzButtonVariant.tertiary && _currentState == AppButtonState.hover) {
      return Text(
        String.fromCharCode(widget.icon!.codePoint),
        style: TextStyle(
          fontFamily: widget.icon!.fontFamily,
          package: widget.icon!.fontPackage,
      fontSize: _kIconSize,
          color: _textColor,
          shadows: _textShadows,
        ),
      );
    }
    return Icon(
      widget.icon,
      color: _textColor,
    size: _kIconSize,
    );
  }

  Border? get _border {
    switch (widget.variant) {
      case ZuzButtonVariant.primary:
        return null;
      case ZuzButtonVariant.secondary:
        // Sempre exibe a borda cinza-4, conforme DS
        return Border.all(color: AppColors.gray4, width: 1);
      case ZuzButtonVariant.destructive:
        if (_currentState == AppButtonState.disabled) {
          return Border.all(color: AppColors.gray4, width: 1);
        }
        if (_currentState == AppButtonState.hover || _currentState == AppButtonState.pressed) {
          return Border.all(color: AppColors.white, width: 1);
        }
        return null;
      case ZuzButtonVariant.tertiary:
      case ZuzButtonVariant.icon:
        // Ícone-only normal
        if (_currentState == AppButtonState.hover || _currentState == AppButtonState.pressed) {
          return Border.all(color: AppColors.gray3, width: 1);
        }
        // default/disabled
        return Border.all(color: AppColors.gray2, width: 1);
      case ZuzButtonVariant.iconDestructive:
        // Ícone-only destructive
        if (_currentState == AppButtonState.hover) {
          return Border.all(color: AppColors.error.withOpacity(0.2), width: 1);
        }
        if (_currentState == AppButtonState.pressed) {
          return Border.all(color: AppColors.error, width: 1);
        }
        // default/disabled
        return Border.all(color: AppColors.gray2, width: 1);
    }
  }

  EdgeInsets get _contentPadding {
    // Icon variants usam padding 8; demais seguem 14/20
    if (_isIconVariant) {
      return const EdgeInsets.all(8);
    }
    if (_isTertiary) {
      // Tertiary sem padding
      return EdgeInsets.zero;
    }
    return const EdgeInsets.symmetric(vertical: _kPaddingV, horizontal: _kPaddingH);
  }

  // Builder separado para loader (mantém layout original e reutiliza valores)
  Widget _buildLoader() {
    return SizedBox(
      width: _kIconSize,
      height: _kIconSize,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(_textColor),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: Focus(
        onFocusChange: (focused) => setState(() => _isFocused = focused),
        child: GestureDetector(
          onTapDown: (_) => setState(() => _isPressed = true),
          onTapUp: (_) => setState(() => _isPressed = false),
          onTapCancel: () => setState(() => _isPressed = false),
          onTap: widget.isLoading ? null : widget.onPressed,
          child: AnimatedContainer(
            duration: _kAnimDuration,
            curve: Curves.easeInOut,
            width: widget.width,
            // Para Icon e Tertiary, deixamos altura automática (wrap content)
            height: _effectiveHeight,
            decoration: _isTertiary
                ? null
                : BoxDecoration(
                    color: _backgroundColor,
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: _boxShadow,
                    border: _border,
                  ),
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: _contentPadding,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (widget.isLoading) ...[
                      _buildLoader(),
                      // Só insere gap se houver texto depois (mantém lógica original)
                      if (_needsLoaderGap)
                        const SizedBox(width: 8),
                    ] else if (widget.icon != null) ...[
                      _buildIcon()!,
                      // Para variantes com texto, gap 4 entre ícone e texto
                      if (!_isIconVariant)
                        const SizedBox(width: 4),
                    ],
                    if (_showsText)
                      Flexible(
                        child: Text(
                          widget.text,
                          style: _effectiveTextStyle,
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                          maxLines: widget.maxLines,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
