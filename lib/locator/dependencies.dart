import '../app/states/app_state.dart';
import '../app/states/app_state_implementation.dart';
import '../auth/login/repositories/login_repository.dart';
import '../auth/login/repositories/login_repository_implementation.dart';
import '../auth/login/services/email_password_login_service_implementation.dart';
import '../auth/login/services/email_password_login_service.dart';
import '../auth/login/services/session_storage_service_implementation.dart';
import '../auth/login/services/session_storage_sevice.dart';
import '../auth/login/states/email_password_login_state.dart';
import '../auth/login/states/email_password_login_state_implementation.dart';
import '../auth/onboarding/states/onboarding_state.dart';
import '../auth/onboarding/states/onboarding_state_implementation.dart';
import '../error/state/error_handler_state.dart';
import '../error/state/error_handler_state_implementation.dart';
import '../http_client/services/http_client_service.dart';
import '../http_client/services/http_client_service_implementation.dart';
import '../local_storage/services/local_storage_service.dart';
import '../local_storage/services/local_storage_service_implementation.dart';
import '../menu/states/menu_state.dart';
import '../menu/states/menu_state_implementation.dart';
import 'locator.dart';
import '../localization/repositories/locale_repository.dart';
import '../localization/repositories/locale_repository_implementation.dart';
import '../navigation/repositories/navigation_repository.dart';
import '../navigation/repositories/navigation_repository_implementation.dart';

/// Inicializa e registra todas as dependências da aplicação.
///
/// Mantém todos os serviços, repositórios e states organizados por módulo,
/// facilitando manutenção, leitura e testes. Deve ser chamado no `main()` via
/// `initializeDependencies()` antes de `runApp`.
///
/// - Preferir `registerLazySingleton` para criação sob demanda.
/// - Respeitar a organização por módulos para manter baixo acoplamento.
void initializeDependencies() {
  // Compartilhados
  Locator.instance.registerLazySingleton<HttpClientService>(
    () => HttpClientServiceImplementation(useMockApi: true),
  );
  Locator.instance.registerLazySingleton<LocalStorageService>(
    () => LocalStorageServiceImplementation(),
  );
  Locator.instance.registerLazySingleton<LocaleRepository>(
    () => LocaleRepositoryImplementation(
      localStorageService: Locator.instance.get<LocalStorageService>(),
    ),
  );
  Locator.instance.registerLazySingleton<NavigationRepository>(
    () => NavigationRepositoryImplementation(),
  );
  Locator.instance.registerLazySingleton<ErrorHandlerState>(
    () => ErrorHandlerStateImplementation(
      navigationRepository: Locator.instance.get<NavigationRepository>(),
      localeRepository: Locator.instance.get<LocaleRepository>(),
    ),
  );

  // Módulo: App
  Locator.instance.registerLazySingleton<AppState>(
    () => AppStateImplementation(
      navigationRepository: Locator.instance.get<NavigationRepository>(),
      localeRepository: Locator.instance.get<LocaleRepository>(),
    ),
  );

  // Módulo: Auth/Onboarding
  Locator.instance.registerLazySingleton<OnboardingState>(
    () => OnboardingStateImplementation(
      localeRepository: Locator.instance.get<LocaleRepository>(),
      navigationRepository: Locator.instance.get<NavigationRepository>(),
    ),
  );

  // Módulo: Auth/Login
  Locator.instance.registerLazySingleton<SessionStorageService>(
    () => SessionStorageServiceImplementation(
      localStorage: Locator.instance.get<LocalStorageService>(),
    ),
  );
  Locator.instance.registerLazySingleton<EmailPasswordLoginService>(
    () => EmailPasswordLoginServiceImplementation(
      httpClient: Locator.instance.get<HttpClientService>(),
    ),
  );
  Locator.instance.registerLazySingleton<LoginRepository>(
    () => LoginRepositoryImplementation(
      loginService: Locator.instance.get<EmailPasswordLoginService>(),
      sessionService: Locator.instance.get<SessionStorageService>(),
    ),
  );
  Locator.instance.registerLazySingleton<EmailPasswordLoginState>(
    () => EmailPasswordLoginStateImplementation(
      loginRepository: Locator.instance.get<LoginRepository>(),
      localeRepository: Locator.instance.get<LocaleRepository>(),
      navigationRepository: Locator.instance.get<NavigationRepository>(),
    ),
  );

  // Menu
  Locator.instance.registerLazySingleton<MenuState>(
    () => MenuStateImplementation(
      navigationRepository: Locator.instance.get<NavigationRepository>(),
      localeRepository: Locator.instance.get<LocaleRepository>(),
    ),
  );
}
