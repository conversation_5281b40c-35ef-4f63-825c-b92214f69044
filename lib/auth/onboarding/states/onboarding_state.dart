import 'package:flutter/material.dart';

import '../models/onboarding_strings.dart';

abstract class OnboardingState {
  OnboardingStrings get strings;
  double get logoScale;
  Duration get logoScaleAnimationDuration;
  Alignment get logoAlignment;
  Duration get logoAlignmentAnimationDuration;
  double get contentOpacity;
  Duration get contentOpacityAnimationDuration;
  Future<void> startLogoAnimation();
  void goToLogin();
  void goToFirstAccess();
  void goToSignUp(BuildContext context);
}
