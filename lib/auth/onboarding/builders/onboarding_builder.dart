import 'package:flutter/material.dart';

import '../../../components/icon/zuz_logo.dart';
import '../../../components/scaffold/background_pattern_decoration.dart';
import '../../../components/top_bar/transparent_top_bar.dart';
import '../../../locator/locator.dart';
import '../states/onboarding_state.dart';
import '../views/onboarding_page.dart';

class OnboardingBuilder extends StatelessWidget {
  const OnboardingBuilder({super.key});

  OnboardingState get _state => Locator.instance.get<OnboardingState>();

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _state as ChangeNotifier,
      builder: (context, child) {
        // Inicia animação de logo após primeiro frame
        WidgetsBinding.instance.addPostFrameCallback(
          (_) => _state.startLogoAnimation(),
        );

        // Tela e logo com animação
        return Container(
          decoration: BackgroundPatternDecoration(),
          child: <PERSON><PERSON>(
            children: [
              // Página de onboarding
              AnimatedOpacity(
                duration: _state.contentOpacityAnimationDuration,
                opacity: _state.contentOpacity,
                child: OnboardingPage(
                  topBar: TransparentTopBar(),
                  welcomeText: _state.strings.welcomeText,
                  loginButtonLabel: _state.strings.loginButtonLabel,
                  onLoginPressed: _state.goToLogin,
                  firstAccessButtonLabel: _state.strings.firstAccessButtonLabel,
                  onFirstAccessPressed: _state.goToFirstAccess,
                  signUpText: _state.strings.signUpDecoratedText,
                  onSignUpPressed: () => _state.goToSignUp(context),
                ),
              ),

              // Logo animado
              Padding(
                padding: EdgeInsets.only(top: MediaQuery.viewPaddingOf(context).top + 22),
                child: AnimatedAlign(
                  alignment: _state.logoAlignment,
                  duration: _state.logoAlignmentAnimationDuration,
                  curve: Curves.easeInQuad,
                  child: AnimatedScale(
                    scale: _state.logoScale,
                    duration: _state.logoScaleAnimationDuration,
                    curve: Curves.easeIn,
                    child: ZuzLogo.large(),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
