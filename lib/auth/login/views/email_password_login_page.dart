import 'package:flutter/material.dart';

import '../../../components/scaffold/scaffold_with_centered_body.dart';

class EmailPasswordLoginPage extends StatelessWidget {
  const EmailPasswordLoginPage({
    super.key,
    required this.form<PERSON>ey,
    required this.autovalidateMode,
    required this.emailInput,
    required this.passwordInput,
    required this.keepMeLoggedInInput,
    required this.resetPasswordButton,
    required this.loginButton,
  });

  final GlobalKey<FormState> formKey;
  final AutovalidateMode autovalidateMode;
  final Widget emailInput;
  final Widget passwordInput;
  final Widget keepMeLoggedInInput;
  final Widget resetPasswordButton;
  final Widget loginButton;

  @override
  Widget build(BuildContext context) {
    return ScaffoldWithCenteredBody.largeLogo(
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: formKey,
          autovalidateMode: autovalidateMode,
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Email field
                emailInput,

                // Password field
                const SizedBox(height: 16),
                passwordInput,

                const SizedBox(height: 16),
                Row(
                  children: [
                    // Keep me logged in
                    Flexible(child: keepMeLoggedInInput),

                    // Reset password button
                    const SizedBox(width: 10),
                    Flexible(child: resetPasswordButton),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      footer: loginButton,
    );
  }
}
