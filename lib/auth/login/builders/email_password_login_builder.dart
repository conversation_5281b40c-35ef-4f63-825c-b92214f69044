import 'package:flutter/material.dart';

import '../../../components/button/button.dart';
import '../../../components/checkbox/zuz_checkbox_with_text.dart';
import '../../../components/text_field/zuz_text_field.dart';
import '../../../locator/locator.dart';
import '../states/email_password_login_state.dart';
import '../views/email_password_login_page.dart';

class EmailPasswordLoginBuilder extends StatelessWidget {
  const EmailPasswordLoginBuilder({super.key});

  EmailPasswordLoginState get _state =>
      Locator.instance.get<EmailPasswordLoginState>();

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _state as ChangeNotifier,
      builder: (context, child) {
        return EmailPasswordLoginPage(
          formKey: _state.formKey,
          autovalidateMode: _state.autovalidateMode,
          emailInput: ZuzTextField(
            label: _state.strings.emailLabel,
            keyboardType: TextInputType.emailAddress,
            onChanged: _state.onEmailChanged,
            validator: _state.validateEmail,
          ),
          passwordInput: ZuzTextField(
            label: _state.strings.passwordLabel,
            isPassword: true,
            onChanged: _state.onPasswordChanged,
            validator: _state.validatePassword,
          ),
          resetPasswordButton: ZuzButton(
            variant: ZuzButtonVariant.tertiary,
            text: _state.strings.resetPasswordButtonLabel,
            maxLines: 2,
            onPressed: _state.resetPassword,
          ),
          keepMeLoggedInInput: ZuzCheckboxWithText(
            value: _state.keepMeLoggedIn,
            text: _state.strings.keepMeLoggedInLabel,
            onChanged: _state.onKeepMeLoggedInChanged,
          ),
          loginButton: ZuzButton(
            width: double.infinity,
            text: _state.strings.loginButtonLabel,
            onPressed: _state.loginAction.execute,
          ),
        );
      },
    );
  }
}
