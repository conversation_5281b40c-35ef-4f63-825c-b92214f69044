import 'package:flutter/material.dart';

import '../../../localization/repositories/locale_repository.dart';
import '../../../navigation/models/assessments_route.dart';
import '../../../navigation/models/onboarding_route.dart';
import '../../../navigation/repositories/navigation_repository.dart';
import '../../../utils/async_action.dart';
import '../../../utils/validators.dart';
import '../repositories/login_repository.dart';
import '../models/login_strings.dart';
import 'email_password_login_state.dart';

class EmailPasswordLoginStateImplementation extends ChangeNotifier
    implements EmailPasswordLoginState {
  EmailPasswordLoginStateImplementation({
    required LoginRepository loginRepository,
    required LocaleRepository localeRepository,
    required NavigationRepository navigationRepository,
  }) : _loginRepository = loginRepository,
       _localeRepository = localeRepository,
       _navigationRepository = navigationRepository;

  final LoginRepository _loginRepository;
  final LocaleRepository _localeRepository;
  final NavigationRepository _navigationRepository;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  AutovalidateMode _autovalidateMode = AutovalidateMode.disabled;
  String _email = '';
  String _password = '';
  bool _keepMeLoggedIn = true;

  @override
  LoginStrings get strings => _localeRepository.strings.loginStrings;

  @override
  GlobalKey<FormState> get formKey => _formKey;

  @override
  AutovalidateMode get autovalidateMode => _autovalidateMode;

  @override
  String get email => _email;

  @override
  String get password => _password;

  @override
  void onEmailChanged(String email) {
    _email = email;
  }

  @override
  String? validateEmail(String? email) {
    final isEmailValid = Validators.validateRequired(email);
    if (!isEmailValid) return strings.invalidEmailError;
    return null;
  }

  @override
  void onPasswordChanged(String password) {
    _password = password;
  }

  @override
  String? validatePassword(String? password) {
    final isPasswordValid = Validators.validateRequired(password);
    if (!isPasswordValid) return strings.invalidPasswordError;
    return null;
  }

  @override
  bool get keepMeLoggedIn => _keepMeLoggedIn;

  @override
  void onKeepMeLoggedInChanged(bool? keepMeLoggedIn) {
    _keepMeLoggedIn = keepMeLoggedIn ?? false;
    notifyListeners();
  }

  @override
  late final loginAction = AsyncAction(action: _onLoginPressed);

  Future<void> _onLoginPressed() async {
    _autovalidateMode = AutovalidateMode.always;
    notifyListeners();
    if (!formKey.currentState!.validate()) return;
    await _loginRepository.loginWithEmailAndPassword(
      email: _email,
      password: _password,
      storeToken: _keepMeLoggedIn,
    );
    _navigationRepository.to(AssessmentsRoute());
  }

  @override
  void resetPassword() {
    _navigationRepository.to(PasswordResetRoute());
  }
}
