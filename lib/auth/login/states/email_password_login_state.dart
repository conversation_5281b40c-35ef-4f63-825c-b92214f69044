import 'package:flutter/widgets.dart';

import '../../../utils/async_action.dart';
import '../models/login_strings.dart';

abstract class EmailPasswordLoginState {
  LoginStrings get strings;

  GlobalKey<FormState> get formKey;
  AutovalidateMode get autovalidateMode;

  String get email;
  void onEmailChanged(String email);
  String? validateEmail(String? price);

  String get password;
  void onPasswordChanged(String password);
  String? validatePassword(String? price);

  bool get keepMeLoggedIn;
  void onKeepMeLoggedInChanged(bool? keepMeLoggedIn);

  AsyncAction get loginAction;

  void resetPassword();
}
