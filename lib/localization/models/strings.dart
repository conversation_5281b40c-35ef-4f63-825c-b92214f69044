
import '../../auth/login/models/login_strings.dart';
import '../../auth/onboarding/models/onboarding_strings.dart';
import '../../components/models/components_strings.dart';
import '../../error/models/error_strings.dart';
import '../../http_client/models/http_strings.dart';
import '../../menu/models/menu_strings.dart';
import '../../utils/utils_strings.dart';

/// Agregador de strings internacionalizadas (i18n) por domínio do app.
///
/// Cada módulo do sistema define sua própria interface de strings (ex.:
/// `HttpStrings`, `LoginStrings`) e respectivas implementações por idioma.
/// As implementações concretas de `Strings` (ex.: `StringsPtBr`) expõem
/// todas essas interfaces para facilitar o acesso em States/Views.
abstract class Strings {
  /// Textos de erros (módulo `error`).
  ErrorStrings get errorStrings;
  /// Textos de utilitários (módulo `utils`).
  UtilsStrings get utilsStrings;
  /// Textos do cliente HTTP (módulo `http_client`).
  HttpStrings get httpStrings;
  /// Textos de login/autenticação (módulo `auth/login`).
  LoginStrings get loginStrings;
  /// Textos dos componentes (módulo `components`).
  ComponentsStrings get componentsStrings;
  /// Textos do onboarding (módulo `auth/onboarding`).
  OnboardingStrings get onboardingStrings;
  /// Textos do menu (módulo `menu`).
  MenuStrings get menuStrings;
}
