import 'package:zuz_app/error/models/error_strings.dart';

import '../../auth/login/models/login_strings.dart';
import '../../auth/login/models/login_strings_pt_br.dart';
import '../../auth/onboarding/models/onboarding_strings.dart';
import '../../auth/onboarding/models/onboarding_strings_pt_br.dart';
import '../../components/models/components_strings.dart';
import '../../components/models/components_strings_pt_br.dart';
import '../../error/models/error_strings_pt_br.dart';
import '../../http_client/models/http_strings.dart';
import '../../http_client/models/http_strings_pt_br.dart';
import '../../menu/models/menu_strings.dart';
import '../../menu/models/menu_strings_pt_br.dart';
import '../../utils/utils_strings.dart';
import '../../utils/utils_strings_pt_br.dart';
import 'strings.dart';

/// Implementação de `Strings` para o idioma pt_BR.
class StringsPtBr extends Strings {
  @override
  ErrorStrings get errorStrings => ErrorStringsPtBr();

  @override
  UtilsStrings get utilsStrings => UtilsStringsPtBr();

  @override
  HttpStrings get httpStrings => HttpStringsPtBr();

  @override
  LoginStrings get loginStrings => LoginStringsPtBr();

  @override
  ComponentsStrings get componentsStrings => ComponentsStringsPtBr();

  @override
  OnboardingStrings get onboardingStrings => OnboardingStringsPtBr();

  @override
  MenuStrings get menuStrings => MenuStringsPtBr();
}
