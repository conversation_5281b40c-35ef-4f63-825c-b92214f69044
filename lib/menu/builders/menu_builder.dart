import 'package:flutter/material.dart';

import '../../locator/locator.dart';
import '../states/menu_state.dart';
import '../views/side_menu.dart';

class MenuBuilder extends StatelessWidget {
  const MenuBuilder({super.key});

  MenuState get _state => Locator.instance.get<MenuState>();

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _state as ChangeNotifier,
      builder: (context, child) {
        return SideMenu(
          operationsTitle: _state.strings.operationsTitle,
          assessmentsLabel: _state.strings.assessments,
          onAssessmentsTap: _state.showAssessments,
          herdLabel: _state.strings.herd,
          onHerdTap: _state.showHerd,
          propertiesLabel: _state.strings.properties,
          onPropertiesTap: _state.showProperties,
          packagesLabel: _state.strings.packages,
          onPackagesTap: _state.showPackages,
          configurationTitle: _state.strings.configurationTitle,
          teamLabel: _state.strings.team,
          onTeamTap: _state.showTeam,
          accountLabel: _state.strings.account,
          onAccountTap: _state.showAccount,
          logoutLabel: _state.strings.logout,
          onLogoutTap: _state.onLogout,
          onClose: _state.onClose,
        );
      },
    );
  }
}
