 import 'package:flutter/widgets.dart';

import '../../../auth/login/repositories/login_repository.dart';
import '../../../locator/locator.dart';
import '../../../localization/repositories/locale_repository.dart';
import '../../../navigation/models/account_route.dart';
import '../../../navigation/models/assessments_route.dart';
import '../../../navigation/models/herd_route.dart';
import '../../../navigation/models/onboarding_route.dart';
import '../../../navigation/models/packages_route.dart';
import '../../../navigation/models/properties_route.dart';
import '../../../navigation/repositories/navigation_repository.dart';
import '../../navigation/models/team_route.dart';
import '../models/menu_strings.dart';
import 'menu_state.dart';

class MenuStateImplementation extends ChangeNotifier implements MenuState {
  MenuStateImplementation({
    required NavigationRepository navigationRepository,
    required LocaleRepository localeRepository,
  }) : _navigationRepository = navigationRepository,
       _localeRepository = localeRepository;

  final NavigationRepository _navigationRepository;
  final LocaleRepository _localeRepository;

  @override
  MenuStrings get strings => _localeRepository.strings.menuStrings;

  @override
  void showAssessments() {
    _navigationRepository.to(AssessmentsRoute());
  }

  @override
  void showHerd() {
    _navigationRepository.to(HerdRoute());
  }

  @override
  void showProperties() {
    _navigationRepository.to(PropertiesRoute());
  }

  @override
  void showPackages() {
    _navigationRepository.to(PackagesRoute());
  }

  @override
  void showTeam() {
    _navigationRepository.to(TeamRoute());
  }

  @override
  void showAccount() {
    _navigationRepository.to(AccountRoute());
  }

  @override
  void onLogout() {
    final loginRepository = Locator.instance.get<LoginRepository>();
    loginRepository.logout();
    _navigationRepository.to(OnboardingRoute());
  }

  @override
  void onClose() {
    _navigationRepository.pop();
  }
}
