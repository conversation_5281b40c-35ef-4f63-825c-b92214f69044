import 'package:flutter/material.dart';

import '../../components/icon/zuz_icons.dart';
import '../../components/icon/zuz_logo.dart';
import '../../components/scaffold/background_pattern_decoration.dart';
import '../../theme/colors.dart';

class SideMenu extends StatelessWidget {
  const SideMenu({
    super.key,
    required this.operationsTitle,
    required this.assessmentsLabel,
    required this.onAssessmentsTap,
    required this.herdLabel,
    required this.onHerdTap,
    required this.propertiesLabel,
    required this.onPropertiesTap,
    required this.packagesLabel,
    required this.onPackagesTap,
    required this.configurationTitle,
    required this.teamLabel,
    required this.onTeamTap,
    required this.accountLabel,
    required this.onAccountTap,
    required this.logoutLabel,
    required this.onLogoutTap,
    required this.onClose,
  });

  final String operationsTitle;
  final String assessmentsLabel;
  final VoidCallback onAssessmentsTap;
  final String herdLabel;
  final VoidCallback onHerdTap;
  final String propertiesLabel;
  final VoidCallback onPropertiesTap;
  final String packagesLabel;
  final VoidCallback onPackagesTap;
  final String configurationTitle;
  final String teamLabel;
  final VoidCallback onTeamTap;
  final String accountLabel;
  final VoidCallback onAccountTap;
  final String logoutLabel;
  final VoidCallback onLogoutTap;
  final VoidCallback onClose;

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Container(
        decoration: BackgroundPatternDecoration(isUpsideDown: true),
        child: SafeArea(
          child: Column(
            children: [
              // Cabeçalho
              // DrawerHeader(
              //   child: Row(
              //     children: [
              //       // Logo
              //       ZuzLogo.small(),
        
              //       // Botão para fechar
              //       IconButton(
              //         icon: const Icon(ZuzIcons.close),
              //         onPressed: onClose,
              //       ),
              //     ],
              //   ),
              // ),
              Row(
                children: [
                  // Logo
                  ZuzLogo.small(),
        
                  // Botão para fechar
                  IconButton(
                    icon: const Icon(ZuzIcons.close),
                    onPressed: onClose,
                  ),
                ],
              ),
        
              // Menu
              Flexible(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 48),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Operação ZUZ
                      _MenuTitle(context, operationsTitle),
                      _MenuItem(context, assessmentsLabel, onAssessmentsTap),
                      _MenuItem(context, herdLabel, onHerdTap),
                      _MenuItem(context, propertiesLabel, onPropertiesTap),
                      _MenuItem(context, packagesLabel, onPackagesTap),
        
                      // Configuração
                      Divider(height: 20, color: AppColors.gray4),
                      _MenuTitle(context, configurationTitle),
                      _MenuItem(context, teamLabel, onTeamTap),
                      _MenuItem(context, accountLabel, onAccountTap),
        
                      // Sair
                      Spacer(),
                      _MenuItem(
                        context,
                        logoutLabel,
                        onLogoutTap,
                        Icon(ZuzIcons.logout, color: AppColors.error),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _MenuTitle extends ListTile {
  _MenuTitle(BuildContext context, String text)
    : super(
        title: Text(text),
        titleTextStyle: TextTheme.of(
          context,
        ).bodySmall?.copyWith(color: AppColors.gray5),
        dense: true,
      );
}

class _MenuItem extends ListTile {
  _MenuItem(
    BuildContext context,
    String text, [
    VoidCallback? onTap,
    Widget? leading,
  ]) : super(
         leading: leading,
         title: Text(text),
         titleTextStyle: TextTheme.of(context).bodyMedium,
         onTap: onTap,
       );
}
