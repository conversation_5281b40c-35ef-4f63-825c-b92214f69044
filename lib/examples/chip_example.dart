import 'package:flutter/material.dart';

import '../components/cards/zuz_chip.dart';
import '../theme/colors.dart';

/// Página de exemplo para demonstrar o uso do ZuzChip
class ChipExamplePage extends StatefulWidget {
  const ChipExamplePage({super.key});

  @override
  State<ChipExamplePage> createState() => _ChipExamplePageState();
}

class _ChipExamplePageState extends State<ChipExamplePage> {
  final List<Map<String, dynamic>> _chips = [
    {'id': 1, 'label': 'Neutral', 'type': ChipType.neutral},
    {'id': 2, 'label': 'Success', 'type': ChipType.success},
    {'id': 3, 'label': 'Warning', 'type': ChipType.warning},
    {'id': 4, 'label': 'Error', 'type': ChipType.error},
  {'id': 5, 'label': 'Info', 'type': ChipType.icon, 'iconWidget': Icon(Icons.info_outline, size: 20, color: AppColors.systemInfoTexts)},
  ];

  void _removeChip(int id) {
    setState(() {
      _chips.removeWhere((c) => c['id'] == id);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chips Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'ZuzChip Examples',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _chips.map((c) {
                  final type = c['type'] as ChipType;
                  return ZuzChip(
                    value: type == ChipType.icon ? null : (c['label'] as String),
                    icon: type == ChipType.icon ? (c['iconWidget'] as Widget) : null,
                    type: type,
                    onDeleted: () => _removeChip(c['id'] as int),
                  );
                }).toList(),
              ),
              const SizedBox(height: 24),
              const Text(
                'Sem bullet',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _chips.map((c) {
                  final type = c['type'] as ChipType;
                  return ZuzChip(
                    value: type == ChipType.icon ? null : (c['label'] as String),
                    icon: type == ChipType.icon ? (c['iconWidget'] as Widget) : null,
                    type: type,
                    onDeleted: () => _removeChip(c['id'] as int),
                    showBullet: false,
                  );
                }).toList(),
              ),
              const SizedBox(height: 24),
              const Text(
                'Sem botão de fechar',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _chips.map((c) {
                  final type = c['type'] as ChipType;
                  return ZuzChip(
                    value: type == ChipType.icon ? null : (c['label'] as String),
                    icon: type == ChipType.icon ? (c['iconWidget'] as Widget) : null,
                    type: type,
                    // desativa o close para demonstrar a variação
                    showClose: false,
                  );
                }).toList(),
              ),
              const SizedBox(height: 24),
              const Text(
                'Sem bullet e sem botão de fechar',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _chips.map((c) {
                  final type = c['type'] as ChipType;
                  return ZuzChip(
                    value: type == ChipType.icon ? null : (c['label'] as String),
                    icon: type == ChipType.icon ? (c['iconWidget'] as Widget) : null,
                    type: type,
                    showBullet: false,
                    showClose: false,
                  );
                }).toList(),
              ),
              const SizedBox(height: 24),
              const Text('Tip: clique no X para remover o chip.'),
            ],
          ),
        ),
      ),
    );
  }
}
