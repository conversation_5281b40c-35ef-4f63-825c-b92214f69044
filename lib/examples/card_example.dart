import 'package:flutter/material.dart';

import '../components/button/button.dart';
import '../components/cards/zuz_card.dart';
import '../components/cards/zuz_chip.dart';

/// Página de exemplo para demonstrar o uso do ZuzCard
class CardExamplePage extends StatelessWidget {
  const CardExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ZuzCard Example'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ZuzCard(
              title: 'Lote 02',
              data: [
                {'label': 'Animais (un)', 'value': '300'},
                {'label': 'Avaliações (un)', 'value': '100'},
                {'label': 'Propriedade', 'value': 'Boas Novas'},
                {'label': 'Propriet<PERSON><PERSON>', 'value': '<PERSON>'},
              ],
              statusWidget: const Zuz<PERSON>hip(
                value: 'Em processamento',
                type: ChipType.warning,
                showBullet: false,
                showClose: false,
              ),
              onOptionsPressed: () {
                // exemplo de ação
              },
              miniCards: true,
              miniCardsTitle: 'Resumo abate lote',
              miniCardsData: [
                {'label': 'Ciclo abate', 'value': '23 m'},
                {'label': 'Peso abate', 'value': '555 kg'},
                {'label': 'Rend. carcaça', 'value': '51%'},
                {'label': 'Idade', 'value': '24 m'},
              ],
              actionButton: ZuzButton(
                text: 'Ver avaliações',
                variant: ZuzButtonVariant.secondary,
                onPressed: () {
                  // ação do botão
                },
              ),
            ),
            const SizedBox(height: 16),
            // Segundo card seguindo o exemplo selecionado no Figma
            ZuzCard(
              title: '21/12/24',
              data: [
                {'label': 'Utilizado', 'value': '20'},
                {'label': 'Mínimo diário', 'value': '50'},
                {'label': 'Total consumido', 'value': '50'},
              ],
              miniCards: false,
              // sem onOptionsPressed e sem actionButton
            ),
          ],
        ),
      ),
    );
  }
}
