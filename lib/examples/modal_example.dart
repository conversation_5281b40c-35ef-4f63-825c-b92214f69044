import 'package:flutter/material.dart';

import '../components/button/button.dart';
import '../components/modal/zuz_modal/zuz_modal.dart';
import '../theme/colors.dart';

/// Página de exemplo demonstrando o uso do ZuzModal
class ModalExamplePage extends StatelessWidget {
  const ModalExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Modal Examples'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'ZuzModal Examples',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // Modal simples com título, descrição, ícone e ações (primary primeiro)
            ZuzButton(
              text: 'Modal simples',
              onPressed: () {
                ZuzModal.show(
                  context,
                  icon: const Icon(Icons.check_circle, color: AppColors.success, size: 40),
                  title: 'Título do Modal',
                  description: 'Uma breve descrição do conteúdo do modal.',
                  actionButtons: [
                    // Primary primeiro
                    ZuzButton(
                      text: 'Confirmar',
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                    // Secondary depois
                    ZuzButton(
                      text: 'Cancelar',
                      variant: ZuzButtonVariant.secondary,
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),

            // Modal com ícone
            ZuzButton(
              text: 'Modal com ícone',
              variant: ZuzButtonVariant.secondary,
              onPressed: () {
                ZuzModal.show(
                  context,
                  icon: const Icon(Icons.info, color: AppColors.systemInfo),
                  title: 'Informação Importante',
                  description: 'Este modal contém um ícone representativo.',
                  actionButtons: [
                    ZuzButton(
                      text: 'Entendi',
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),

            // Modal sem botão de fechar
            ZuzButton(
              text: 'Modal sem botão de fechar',
              variant: ZuzButtonVariant.secondary,
              onPressed: () {
                ZuzModal.show(
                  context,
                  title: 'Sem X de Fechar',
                  description: 'Para fechar, use um dos botões de ação.',
                  showCloseButton: false,
                  actionButtons: [
                    ZuzButton(
                      text: 'Fechar',
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),

            // Modal com child customizado
            ZuzButton(
              text: 'Modal com conteúdo customizado',
              variant: ZuzButtonVariant.secondary,
              onPressed: () {
                ZuzModal.show(
                  context,
                  title: 'Formulário Rápido',
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: const [
                      Text('Conteúdo personalizado dentro do modal.'),
                      SizedBox(height: 8),
                      TextField(
                        decoration: InputDecoration(
                          border: OutlineInputBorder(),
                          hintText: 'Digite algo...',
                        ),
                      ),
                    ],
                  ),
                  actionButtons: [
                    ZuzButton(
                      text: 'Cancelar',
                      variant: ZuzButtonVariant.secondary,
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                    ZuzButton(
                      text: 'Salvar',
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),

            // Modal sem título/descrição
            ZuzButton(
              text: 'Modal sem título/descrição',
              variant: ZuzButtonVariant.secondary,
              onPressed: () {
                ZuzModal.show(
                  context,
                  child: const Text('Apenas conteúdo principal sem título e descrição.'),
                  actionButtons: [
                    ZuzButton(
                      text: 'Fechar',
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),

            // Modal com callback onClose e barrierDismissible false
            ZuzButton(
              text: 'Modal com onClose e travado',
              variant: ZuzButtonVariant.tertiary,
              onPressed: () {
                ZuzModal.show(
                  context,
                  title: 'Ação Necessária',
                  description: 'Clique em Confirmar para continuar.',
                  barrierDismissible: false,
                  onClose: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Modal fechado via botão X')), // apenas exemplo
                    );
                  },
                  actionButtons: [
                    ZuzButton(
                      text: 'Cancelar',
                      variant: ZuzButtonVariant.secondary,
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                    ZuzButton(
                      text: 'Confirmar',
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
