import 'package:flutter/material.dart';

import '../components/checkbox/zuz_box_checkbox.dart';
import '../components/checkbox/zuz_box_radiobutton.dart';
import '../components/checkbox/zuz_checkbox.dart';
import '../components/checkbox/zuz_group_box.dart';
import '../components/checkbox/zuz_radiobutton.dart';
import '../components/checkbox/zuz_switch.dart';
import '../components/date_picker/zuz_date_picker.dart';
import '../components/date_picker/zuz_date_picker_field.dart';

/// Página de exemplo para demonstrar o uso dos widgets ZuzCheckBox, ZuzBoxCheckbox,
/// ZuzRadioButton, ZuzBoxRadioButton e ZuzSwitch
class CheckboxExamplePage extends StatefulWidget {
  const CheckboxExamplePage({super.key});

  @override
  State<CheckboxExamplePage> createState() => _CheckboxExamplePageState();
}

class _CheckboxExamplePageState extends State<CheckboxExamplePage> {
  bool? simpleCheckbox1 = false;
  bool? simpleCheckbox2 = true;
  bool? simpleCheckbox3; // null (indeterminate)
  
  bool? boxCheckbox1 = false;
  bool? boxCheckbox2 = true;
  bool? boxCheckbox3; // null (indeterminate)
  
  String? simpleRadio = 'option1';
  String? boxRadio = 'option2';
  
  bool switch1 = false;
  bool switch2 = true;
  bool switch3 = false;
  
  // Novos estados para os grupos
  List<String> selectedGenders = [];
  String? selectedStatus;
  
  // Estado para o campo de período de avaliação
  ZuzDatePickerResult? selectedPeriod;
  
  // Estado para o exemplo do Figma com helper text
  List<String> selectedPermission = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Checkbox, Radio & Switch Examples'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'ZuzCheckBox Simples',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Checkboxes simples
              Row(
                children: [
                  ZuzCheckBox(
                    value: simpleCheckbox1,
                    onChanged: (value) => setState(() => simpleCheckbox1 = value),
                  ),
                  const SizedBox(width: 8),
                  const Text('Desmarcado'),
                ],
              ),
              const SizedBox(height: 8),
              
              Row(
                children: [
                  ZuzCheckBox(
                    value: simpleCheckbox2,
                    onChanged: (value) => setState(() => simpleCheckbox2 = value),
                  ),
                  const SizedBox(width: 8),
                  const Text('Marcado'),
                ],
              ),
              const SizedBox(height: 8),
              
              Row(
                children: [
                  ZuzCheckBox(
                    value: simpleCheckbox3,
                    onChanged: (value) => setState(() => simpleCheckbox3 = value),
                    tristate: true,
                  ),
                  const SizedBox(width: 8),
                  const Text('Indeterminate (tristate)'),
                ],
              ),
              
              const SizedBox(height: 32),
              
              const Text(
                'ZuzRadioButton Simples',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Radio buttons simples
              Row(
                children: [
                  ZuzRadioButton<String>(
                    value: 'option1',
                    groupValue: simpleRadio,
                    onChanged: (value) => setState(() => simpleRadio = value),
                  ),
                  const SizedBox(width: 8),
                  const Text('Opção 1'),
                ],
              ),
              const SizedBox(height: 8),
              
              Row(
                children: [
                  ZuzRadioButton<String>(
                    value: 'option2',
                    groupValue: simpleRadio,
                    onChanged: (value) => setState(() => simpleRadio = value),
                  ),
                  const SizedBox(width: 8),
                  const Text('Opção 2'),
                ],
              ),
              const SizedBox(height: 8),
              
              Row(
                children: [
                  ZuzRadioButton<String>(
                    value: 'option3',
                    groupValue: simpleRadio,
                    onChanged: (value) => setState(() => simpleRadio = value),
                  ),
                  const SizedBox(width: 8),
                  const Text('Opção 3'),
                ],
              ),
              
              const SizedBox(height: 32),
              
              const Text(
                'ZuzBoxCheckbox com Container',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Box checkboxes
              ZuzBoxCheckbox(
                label: 'Termos de Uso',
                text: 'Aceito os termos de uso e política de privacidade',
                value: boxCheckbox1,
                onChanged: (value) => setState(() => boxCheckbox1 = value),
              ),
              const SizedBox(height: 16),
              
              ZuzBoxCheckbox(
                label: 'Notificações',
                text: 'Receber notificações por email',
                value: boxCheckbox2,
                onChanged: (value) => setState(() => boxCheckbox2 = value),
                isOptional: true,
              ),
              const SizedBox(height: 16),
              
              ZuzBoxCheckbox(
                label: 'Estado Indeterminado',
                text: 'Checkbox com estado tristate ativo',
                value: boxCheckbox3,
                onChanged: (value) => setState(() => boxCheckbox3 = value),
                tristate: true,
              ),
              
              const SizedBox(height: 32),
              
              const Text(
                'ZuzBoxRadioButton com Container',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Box radio buttons
              ZuzBoxRadioButton<String>(
                label: 'Método de Pagamento',
                text: 'Cartão de Crédito',
                value: 'credit',
                groupValue: boxRadio,
                onChanged: (value) => setState(() => boxRadio = value),
              ),
              const SizedBox(height: 16),
              
              ZuzBoxRadioButton<String>(
                label: 'Método de Pagamento',
                text: 'Cartão de Débito',
                value: 'debit',
                groupValue: boxRadio,
                onChanged: (value) => setState(() => boxRadio = value),
              ),
              const SizedBox(height: 16),
              
              ZuzBoxRadioButton<String>(
                label: 'Método de Pagamento',
                text: 'PIX',
                value: 'pix',
                groupValue: boxRadio,
                onChanged: (value) => setState(() => boxRadio = value),
                isOptional: true,
              ),
              
              const SizedBox(height: 32),
              
              const Text(
                'ZuzSwitch',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Switches
              ZuzSwitch(
                text: 'Receber notificações push',
                value: switch1,
                onChanged: (value) => setState(() => switch1 = value),
              ),
              const SizedBox(height: 16),
              
              ZuzSwitch(
                text: 'Modo escuro ativado',
                value: switch2,
                onChanged: (value) => setState(() => switch2 = value),
              ),
              const SizedBox(height: 16),
              
              ZuzSwitch(
                text: 'Sincronização automática',
                value: switch3,
                onChanged: (value) => setState(() => switch3 = value),
              ),
              
              const SizedBox(height: 32),
              
              const Text(
                'ZuzGroupBox - Filtros (Exemplo do Figma)',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Campo de período de avaliação (exato como no Figma)
              ZuzDatePickerField(
                label: 'Período de avaliação',
                type: ZuzDatePickerType.range,
                placeholder: 'MM/AAAA - MM/AAAA',
                onChanged: (result) => setState(() => selectedPeriod = result),
              ),
              
              const SizedBox(height: 16),
              
              // Grupo de checkboxes - Sexo (2 colunas)
              ZuzGroupBox<String>(
                label: 'Sexo',
                type: ZuzGroupBoxType.checkbox,
                layout: ZuzGroupBoxLayout.doubleColumn,
                items: const [
                  ZuzGroupBoxItem(text: 'Macho', value: 'male'),
                  ZuzGroupBoxItem(text: 'Fêmea', value: 'female'),
                ],
                selectedValues: selectedGenders,
                onCheckboxChanged: (values) => setState(() => selectedGenders = values),
              ),
              
              const SizedBox(height: 24),
              
              // Grupo de radio buttons - Status (1 coluna)
              ZuzGroupBox<String>(
                label: 'Status da avaliação',
                type: ZuzGroupBoxType.radioButton,
                layout: ZuzGroupBoxLayout.singleColumn,
                items: const [
                  ZuzGroupBoxItem(text: 'Disponível', value: 'available'),
                  ZuzGroupBoxItem(text: 'Em processamento', value: 'processing'),
                ],
                selectedValue: selectedStatus,
                onRadioChanged: (value) => setState(() => selectedStatus = value),
              ),
              
              const SizedBox(height: 32),
              
              // Exemplo do Figma - Permissões com helperText
              const Text(
                'ZuzGroupBox com Helper Text (Exemplo Figma)',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              ZuzGroupBox<String>(
                label: 'Permissão',
                type: ZuzGroupBoxType.checkbox,
                layout: ZuzGroupBoxLayout.singleColumn,
                items: const [
                  ZuzGroupBoxItem(
                    text: 'Gestor',
                    helperText: 'Possui funcionalidades de gerenciamento: cadastros, edições, exclusões e compra de créditos.',
                    value: 'gestor',
                  ),
                  ZuzGroupBoxItem(
                    text: 'Colaborador',
                    helperText: 'Acesso limitado: apenas solicita avaliações e visualiza informações.',
                    value: 'colaborador',
                  ),
                ],
                selectedValues: selectedPermission,
                onCheckboxChanged: (values) => setState(() => selectedPermission = values),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
