import 'package:flutter/material.dart';

import '../components/tab/zuz_tab.dart';
import 'card_example.dart';
import 'chip_example.dart';
import 'checkbox_example.dart';
import 'date_picker_example.dart';
import 'dropdown_example.dart';
import 'information_example.dart';
import 'modal_example.dart';
import 'text_field_example.dart';
import 'toast_example.dart';
import 'zuz_tab_example.dart';

/// Página que agrupa todos os exemplos de componentes
class ComponentsExamplesPage extends StatelessWidget {
  const ComponentsExamplesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 10,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Exemplos de Componentes'),
          bottom: const ZuzTabBar(
            isScrollable: true,
            tabs: [
              Tab(icon: Icon(Icons.text_fields), text: 'Text Fields'),
              Tab(icon: Icon(Icons.arrow_drop_down), text: 'Dropdowns'),
              Tab(icon: Icon(Icons.check_box), text: 'Selection'),
              Tab(icon: Icon(Icons.calendar_today), text: 'Date Picker'),
              Tab(icon: Icon(Icons.tab), text: 'Tab'),
              Tab(icon: Icon(Icons.notifications), text: 'Toast'),
              Tab(icon: Icon(Icons.info), text: 'Information'),
              Tab(icon: Icon(Icons.credit_card), text: 'Cards'),
              Tab(icon: Icon(Icons.label), text: 'Chips'),
              Tab(icon: Icon(Icons.open_in_new), text: 'Modal'),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            TextFieldExamplePage(),
            ZuzDropdownExample(),
            CheckboxExamplePage(),
            DatePickerExamplePage(),
            ZuzTabExample(),
            ToastExamplePage(),
            InformationExamplePage(),
            CardExamplePage(),
            ChipExamplePage(),
            ModalExamplePage(),
          ],
        ),
      ),
    );
  }
}
