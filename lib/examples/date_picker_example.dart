import 'package:flutter/material.dart';

import '../components/button/button.dart';
import '../components/date_picker/zuz_birth_date_field.dart';
import '../components/date_picker/zuz_date_picker.dart';
import '../components/date_picker/zuz_date_picker_field.dart';
import '../theme/theme.dart';

/// Página de exemplo do ZuzDatePicker
class DatePickerExamplePage extends StatefulWidget {
  const DatePickerExamplePage({super.key});

  @override
  State<DatePickerExamplePage> createState() => _DatePickerExamplePageState();
}

class _DatePickerExamplePageState extends State<DatePickerExamplePage> {
  ZuzDatePickerResult? _singleDateResult;
  ZuzDatePickerResult? _rangeDateResult;
  
  // Novos resultados para os campos de texto
  ZuzDatePickerResult? _fieldSingleResult;
  ZuzDatePickerResult? _fieldRangeResult;
  
  // Estados para o campo de data de nascimento
  int? _birthDay;
  int? _birthMonth;
  int? _birthYear;

  /// Formata uma data para exibição
  String _formatDate(DateTime? date) {
    if (date == null) return 'Não selecionada';
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Mostra o date picker de data única
  Future<void> _showSingleDatePicker() async {
    final result = await showZuzDatePicker(
      context: context,
      type: ZuzDatePickerType.single,
      initialDate: _singleDateResult?.selectedDate,
    );

    if (result != null) {
      setState(() {
        _singleDateResult = result;
      });
    }
  }

  /// Mostra o date picker de intervalo
  Future<void> _showRangeDatePicker() async {
    final result = await showZuzDatePicker(
      context: context,
      type: ZuzDatePickerType.range,
      initialStartDate: _rangeDateResult?.startDate,
      initialEndDate: _rangeDateResult?.endDate,
    );

    if (result != null) {
      setState(() {
        _rangeDateResult = result;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gray1,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Título da página
            const Text(
              'Exemplos do Date Picker',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.black,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Demonstração dos componentes de seleção de data do aplicativo.',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.gray5,
              ),
            ),
            const SizedBox(height: 32),

            // Seção de Data Única
            _buildSection(
              title: 'Seleção de Data Única',
              description: 'Permite selecionar uma única data.',
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ZuzButton(
                    text: 'Selecionar Data',
                    variant: ZuzButtonVariant.primary,
                    onPressed: _showSingleDatePicker,
                    icon: Icons.calendar_today,
                  ),
                  const SizedBox(height: 16),
                  _buildResultCard(
                    title: 'Data Selecionada',
                    content: _formatDate(_singleDateResult?.selectedDate),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Seção de Intervalo de Datas
            _buildSection(
              title: 'Seleção de Intervalo',
              description: 'Permite selecionar um intervalo de datas (data de início e data final).',
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ZuzButton(
                    text: 'Selecionar Intervalo',
                    variant: ZuzButtonVariant.primary,
                    onPressed: _showRangeDatePicker,
                    icon: Icons.date_range,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildResultCard(
                          title: 'Data de Início',
                          content: _formatDate(_rangeDateResult?.startDate),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildResultCard(
                          title: 'Data Final',
                          content: _formatDate(_rangeDateResult?.endDate),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Seção com exemplos com dados pré-selecionados
            _buildSection(
              title: 'Com Dados Pré-selecionados',
              description: 'Exemplos com datas já selecionadas inicialmente.',
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: ZuzButton(
                          text: 'Data Hoje',
                          variant: ZuzButtonVariant.secondary,
                          onPressed: () async {
                            final result = await showZuzDatePicker(
                              context: context,
                              type: ZuzDatePickerType.single,
                              initialDate: DateTime.now(),
                            );
                            if (result != null) {
                              setState(() {
                                _singleDateResult = result;
                              });
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ZuzButton(
                          text: 'Última Semana',
                          variant: ZuzButtonVariant.secondary,
                          onPressed: () async {
                            final today = DateTime.now();
                            final weekAgo = today.subtract(const Duration(days: 7));
                            final result = await showZuzDatePicker(
                              context: context,
                              type: ZuzDatePickerType.range,
                              initialStartDate: weekAgo,
                              initialEndDate: today,
                            );
                            if (result != null) {
                              setState(() {
                                _rangeDateResult = result;
                              });
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Seção dos campos de texto (como no Figma)
            _buildSection(
              title: 'Campos de Texto com Date Picker',
              description: 'Campos que abrem o date picker ao serem clicados, mantendo a aparência de TextField.',
              child: Column(
                children: [
                  // Campo de data única
                  ZuzDatePickerField(
                    label: 'Data de nascimento',
                    type: ZuzDatePickerType.single,
                    placeholder: 'DD/MM/AAAA',
                    helperText: 'Selecione sua data de nascimento',
                    initialDate: _fieldSingleResult?.selectedDate,
                    onChanged: (result) => setState(() => _fieldSingleResult = result),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Campo de período (como no Figma)
                  ZuzDatePickerField(
                    label: 'Período de avaliação',
                    type: ZuzDatePickerType.range,
                    placeholder: 'MM/AAAA - MM/AAAA',
                    helperText: 'Selecione o período de avaliação',
                    initialStartDate: _fieldRangeResult?.startDate,
                    initialEndDate: _fieldRangeResult?.endDate,
                    onChanged: (result) => setState(() => _fieldRangeResult = result),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Resultados dos campos
                  Row(
                    children: [
                      Expanded(
                        child: _buildResultCard(
                          title: 'Data Única Campo',
                          content: _formatDate(_fieldSingleResult?.selectedDate),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildResultCard(
                          title: 'Período Campo',
                          content: _fieldRangeResult?.hasSelection == true
                              ? '${_formatDate(_fieldRangeResult!.startDate)} - ${_formatDate(_fieldRangeResult!.endDate)}'
                              : 'Não selecionado',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Seção de Data de Nascimento
            _buildSection(
              title: 'Campo de Data de Nascimento',
              description: 'Campo especializado para entrada de data de nascimento com cálculo de idade.',
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Campo de data de nascimento com dia opcional
                  ZuzBirthDateField(
                    label: 'Data de nascimento',
                    isDayOptional: true,
                    initialDay: _birthDay,
                    initialMonth: _birthMonth,
                    initialYear: _birthYear,
                    onChanged: (day, month, year) {
                      setState(() {
                        _birthDay = day;
                        _birthMonth = month;
                        _birthYear = year;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Campo de data de nascimento sem dia opcional
                  ZuzBirthDateField(
                    label: 'Data de nascimento (dia obrigatório)',
                    isDayOptional: false,
                    onChanged: (day, month, year) {
                      // Apenas para demonstração, não salvamos estes valores
                      print('Data completa: $day/$month/$year');
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Resultado da data de nascimento
                  _buildResultCard(
                    title: 'Data de Nascimento',
                    content: _birthDay != null || _birthMonth != null || _birthYear != null
                        ? '${_birthDay?.toString().padLeft(2, '0') ?? '--'}/${_birthMonth?.toString().padLeft(2, '0') ?? '--'}/${_birthYear ?? '----'}'
                        : 'Não preenchida',
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Informações sobre uso
            ],
        ),
      ),
    );
  }

  /// Constrói uma seção com título e descrição
  Widget _buildSection({
    required String title,
    required String description,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.gray5,
            ),
          ),
          const SizedBox(height: 20),
          child,
        ],
      ),
    );
  }

  /// Constrói um card de resultado
  Widget _buildResultCard({
    required String title,
    required String content,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.gray1,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.gray3),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppColors.gray5,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.black,
            ),
          ),
        ],
      ),
    );
  }
}
