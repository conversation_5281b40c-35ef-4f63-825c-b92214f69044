import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../components/notifications/zuz_toast.dart';
import '../localization/repositories/locale_repository.dart';
import '../locator/locator.dart';

/// Copia o texto de [text] para a área de transferência e mostra um toast de sucesso, caso [showToast] seja `true`.
// void copyToClipboard(String text, {bool showToast = true}) {
void copyToClipboard(
  String text,
  BuildContext context, {
  bool showToast = true,
}) {
  // Copia o texto para a área de transferência
  final data = ClipboardData(text: text);
  Clipboard.setData(data);

  // Retorna caso não deva mostrar toast
  if (!showToast) return;

  // Mostra toast
  final strings = Locator.instance.get<LocaleRepository>().strings;
  ZuzToastManager.showSuccess(
    context,
    message: strings.utilsStrings.copiedToClipboard,
  );
}
