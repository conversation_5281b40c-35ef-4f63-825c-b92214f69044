import 'dart:io';

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:zuz_app/locator/locator.dart';

import '../components/button/button.dart';
import '../components/modal/zuz_modal/zuz_modal.dart';
import '../localization/repositories/locale_repository.dart';
import 'clipboard.dart';

Future<bool> openUrl(
  String url,
  BuildContext context, {

  /// Android usa abertura em aplicação externa. Isso evita que fique uma tela preta ao tentar abrir uma URL na webview, que é usada por padrão.
  bool useExternalApplicationForIos = false,

  /// Descrição do erro a ser mostrada no modal. Quando nulo, usa `strings.utilsStrings.openUrlError`.
  String? errorDescription,

  /// Texto do botão de copiar. Quando nulo, usa `strings.utilsStrings.copyUrl`.
  String? copyButtonText,
  String? valueToCopy,
}) async {
  final uri = Uri.tryParse(url);
  if (uri != null && await canLaunchUrl(uri)) {
    final mode = Platform.isAndroid || useExternalApplicationForIos
        ? LaunchMode.externalApplication
        : LaunchMode.platformDefault;
    return launchUrl(uri, mode: mode);
  } else {
    // Mostra modal para copiar URL
    if (!context.mounted) return false;
    final strings = Locator.instance.get<LocaleRepository>().strings;
    ZuzModal.show(
      context,
      child: Text(strings.utilsStrings.openUrlError),
      actionButtons: [
        ZuzButton(
          text: strings.utilsStrings.copyUrl,
          onPressed: () {
            Navigator.of(context).pop();
            copyToClipboard(valueToCopy ?? url, context);
          },
        ),
      ],
    );
    return false;
  }
}
