import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:zuz_app/error/models/app_error.dart';
import 'package:zuz_app/error/models/error_strings.dart';
import 'package:zuz_app/error/state/error_handler_state_implementation.dart';
import 'package:zuz_app/localization/models/strings.dart';
import 'package:zuz_app/localization/repositories/locale_repository.dart';
import 'package:zuz_app/navigation/repositories/navigation_repository.dart';

// Mocks
class MockNavigationRepository extends Mock implements NavigationRepository {}

class MockLocaleRepository extends Mock implements LocaleRepository {}

// Implementações de apoio para i18n usadas apenas nos testes
class TestErrorStrings implements ErrorStrings {
  TestErrorStrings(this.unknownError);
  @override
  final String unknownError;
}

class TestStrings implements Strings {
  TestStrings(this.errorStrings);
  @override
  final ErrorStrings errorStrings;

  // Não utilizados nesses testes
  @override
  get httpStrings => throw UnimplementedError();
  @override
  get loginStrings => throw UnimplementedError();
  @override
  get componentsStrings => throw UnimplementedError();
  @override
  get utilsStrings => throw UnimplementedError();
  @override
  get onboardingStrings => throw UnimplementedError();
  @override
  get menuStrings => throw UnimplementedError();
}

class TestAppError implements AppError {
  TestAppError(this.message);
  final String message;
  @override
  String messageFromStrings(Strings strings) => message;
}

void main() {
  late MockNavigationRepository navRepo;
  late MockLocaleRepository localeRepo;

  setUp(() {
    navRepo = MockNavigationRepository();
    localeRepo = MockLocaleRepository();
  });

  Future<BuildContext> pumpAppAndGetContext(WidgetTester tester) async {
    late BuildContext captured;
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (context) {
              captured = context;
              return const SizedBox.shrink();
            },
          ),
        ),
      ),
    );
    // Garante montagem completa do widget tree
    await tester.pump();
    return captured;
  }

  testWidgets('exibe mensagem específica quando erro é AppError', (tester) async {
    final ctx = await pumpAppAndGetContext(tester);

    // Arrange
    when(() => navRepo.context).thenReturn(ctx);
    final strings = TestStrings(TestErrorStrings('GEN'));
    when(() => localeRepo.strings).thenReturn(strings);

    final sut = ErrorHandlerStateImplementation(
      navigationRepository: navRepo,
      localeRepository: localeRepo,
    );

    // Act
    sut.showMessage(TestAppError('Mensagem específica'));
    await tester.pump(); // processa SnackBar

    // Assert
    expect(find.text('Mensagem específica'), findsOneWidget);
  });

  testWidgets('exibe unknownError quando não é AppError', (tester) async {
    final ctx = await pumpAppAndGetContext(tester);

    // Arrange
    when(() => navRepo.context).thenReturn(ctx);
    final strings = TestStrings(TestErrorStrings('Houve um erro desconhecido no app'));
    when(() => localeRepo.strings).thenReturn(strings);

    final sut = ErrorHandlerStateImplementation(
      navigationRepository: navRepo,
      localeRepository: localeRepo,
    );

    // Act
    sut.showMessage(Exception('qualquer'));
    await tester.pump();

    // Assert
    expect(find.text('Houve um erro desconhecido no app'), findsOneWidget);
  });

  testWidgets('não exibe nada quando context é nulo (não montado)', (tester) async {
    await pumpAppAndGetContext(tester); // árvore pronta, mas retornaremos null

    // Arrange
    when(() => navRepo.context).thenReturn(null);
    final strings = TestStrings(TestErrorStrings('GEN'));
    when(() => localeRepo.strings).thenReturn(strings);

    final sut = ErrorHandlerStateImplementation(
      navigationRepository: navRepo,
      localeRepository: localeRepo,
    );

    // Act
    sut.showMessage(Exception('qualquer'));
    await tester.pump();

    // Assert: não há SnackBar na árvore
    expect(find.byType(SnackBar), findsNothing);
  });
}
